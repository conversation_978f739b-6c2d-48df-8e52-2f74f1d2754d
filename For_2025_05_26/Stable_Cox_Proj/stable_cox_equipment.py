"""
Stable Cox 模型主要实现文件
适用于装备材料测量数据的生存分析
"""

import numpy as np
import pandas as pd
import logging
import os
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from config import Config
from data_preprocessing import EquipmentDataProcessor
from For_2025_05_26.Stable_Cox_Proj.model.srdo_algorithm import SRDOReweighter
from cox_model import WeightedCoxModel, CoxFeatureSelector
from evaluation import SurvivalEvaluator
from utils_equipment import (
    setup_logging, create_directories, save_model,
    validate_data_format, print_data_summary, ProgressTracker
)

logger = logging.getLogger(__name__)

class StableCoxEquipment:
    """装备材料 Stable Cox 模型"""

    def __init__(self, config=None):
        """
        初始化 Stable Cox 模型

        参数:
        - config: 配置对象或字典
        """
        self.config = config or Config()
        self.data_processor = None
        self.feature_selector = None
        self.reweighter = None
        self.cox_model = None
        self.evaluator = None
        self.is_fitted = False

        # 设置日志
        setup_logging(
            log_level=self.config.OUTPUT_CONFIG.get('log_level', 'INFO')
        )

        # 创建必要目录
        create_directories([
            self.config.OUTPUT_CONFIG.get('model_dir', 'models'),
            self.config.OUTPUT_CONFIG.get('results_dir', 'results'),
            self.config.EVALUATION_CONFIG.get('plot_dir', 'plots')
        ])

        logger.info("Stable Cox Equipment 模型初始化完成")

    def fit(self, data, duration_col=None, event_col=None, feature_cols=None):
        """
        拟合 Stable Cox 模型

        参数:
        - data: 输入数据 (DataFrame 或文件路径)
        - duration_col: 生存时间列名
        - event_col: 事件指示器列名
        - feature_cols: 特征列名列表

        返回:
        - self
        """
        logger.info("开始拟合 Stable Cox 模型")

        # 1. 数据加载和预处理
        if isinstance(data, str):
            data = pd.read_csv(data)

        # 检查数据是否已经预处理过（包含 survival_time 和 event 列）
        is_preprocessed = 'survival_time' in data.columns and 'event' in data.columns

        if is_preprocessed:
            print_data_summary(data, "预处理后数据")
            processed_data = data.copy()
        else:
            print_data_summary(data, "原始数据")

            # 进行数据预处理
            progress = ProgressTracker(6, "模型训练进度")
            progress.update("数据预处理")

            self.data_processor = EquipmentDataProcessor(self.config.DATA_CONFIG)
            processed_data = self.data_processor.process_data(
                data,
                time_col=duration_col or self.config.DATA_CONFIG['time_col'],
                id_col=self.config.DATA_CONFIG['id_col']
            )

            print_data_summary(processed_data, "预处理后数据")

        # 2. 数据验证
        is_valid, issues = validate_data_format(processed_data,
                                               required_cols=['SN_Common', 'survival_time', 'event'])
        if not is_valid:
            logger.warning(f"数据格式警告: {issues}")

        # 更新列名
        duration_col = duration_col or 'survival_time'
        event_col = event_col or 'event'

        # 3. 特征列处理
        if feature_cols is None:
            if hasattr(self.config, 'get_measurement_columns'):
                feature_cols = self.config.get_measurement_columns(processed_data)
            else:
                # 自动检测特征列
                excluded_cols = {'SN_Common', 'survival_time', 'event', 'Date', 'PO1 PO Number_ERT'}
                feature_cols = [col for col in processed_data.columns if col not in excluded_cols]

        logger.info(f"使用 {len(feature_cols)} 个特征进行建模")

        # 初始化进度跟踪器（如果还没有）
        if not hasattr(self, 'progress') or not is_preprocessed:
            progress = ProgressTracker(6, "模型训练进度")

        # 4. 特征选择
        progress.update("特征选择")
        if self.config.FEATURE_SELECTION_CONFIG['use_feature_selection']:
            self.feature_selector = CoxFeatureSelector(
                method=self.config.FEATURE_SELECTION_CONFIG['method'],
                top_n=self.config.FEATURE_SELECTION_CONFIG['top_n_features'],
                p_threshold=self.config.FEATURE_SELECTION_CONFIG['p_value_threshold']
            )

            # 创建包含所有必要列的数据用于特征选择
            selection_data = processed_data[feature_cols + [duration_col, event_col]].copy()
            self.feature_selector.fit(selection_data, duration_col, event_col)
            selected_features = self.feature_selector.selected_features_

            logger.info(f"特征选择完成，从 {len(feature_cols)} 个特征中选择了 {len(selected_features)} 个")
        else:
            selected_features = feature_cols

        # 5. 样本重加权 (SRDO)
        progress.update("样本重加权")
        weights = None
        if self.config.TRAINING_CONFIG['use_reweighting']:
            self.reweighter = SRDOReweighter(
                p_s=len(selected_features) // 2,  # 假设一半特征为稳定特征
                **self.config.SRDO_CONFIG
            )

            feature_data = processed_data[selected_features].values
            self.reweighter.fit(feature_data)
            weights = self.reweighter.get_weights()

            logger.info(f"SRDO 重加权完成，权重统计 - 均值: {np.mean(weights):.4f}, "
                       f"标准差: {np.std(weights):.4f}")

        # 6. Cox 模型训练
        progress.update("Cox 模型训练")
        self.cox_model = WeightedCoxModel(**self.config.COX_CONFIG)

        # 准备训练数据
        train_data = processed_data[selected_features + [duration_col, event_col]].copy()

        self.cox_model.fit(
            train_data,
            duration_col=duration_col,
            event_col=event_col,
            weights=weights,
            feature_names=selected_features
        )

        # 7. 模型验证
        progress.update("模型验证")
        if self.config.TRAINING_CONFIG['cross_validation']:
            cv_score = self._cross_validate(train_data, duration_col, event_col, weights)
            logger.info(f"交叉验证 C-index: {cv_score:.4f}")

        progress.finish("模型训练完成")

        self.is_fitted = True
        self.feature_names_ = selected_features

        # 保存模型
        if self.config.OUTPUT_CONFIG['save_model']:
            model_path = os.path.join(
                self.config.OUTPUT_CONFIG['model_dir'],
                'stable_cox_equipment_model.pkl'
            )
            save_model(self, model_path)

        logger.info("Stable Cox 模型训练完成")
        return self

    def predict(self, X, return_type='risk_score'):
        """
        预测风险评分或生存函数

        参数:
        - X: 特征数据
        - return_type: 返回类型 ('risk_score', 'survival_function', 'hazard')

        返回:
        - predictions: 预测结果
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用 fit 方法")

        # 确保输入格式正确
        if not isinstance(X, pd.DataFrame):
            X = pd.DataFrame(X, columns=self.feature_names_)

        # 选择训练时使用的特征
        X_features = X[self.feature_names_]

        return self.cox_model.predict(X_features, return_type=return_type)

    def evaluate(self, test_data, duration_col='survival_time', event_col='event'):
        """
        评估模型性能

        参数:
        - test_data: 测试数据
        - duration_col: 生存时间列名
        - event_col: 事件指示器列名

        返回:
        - results: 评估结果
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用 fit 方法")

        logger.info("开始模型评估")

        # 初始化评估器
        self.evaluator = SurvivalEvaluator(
            save_plots=self.config.EVALUATION_CONFIG['save_plots'],
            plot_dir=self.config.EVALUATION_CONFIG['plot_dir']
        )

        # 执行评估
        results = self.evaluator.evaluate_model(
            self.cox_model,
            test_data,
            duration_col=duration_col,
            event_col=event_col,
            feature_cols=self.feature_names_
        )

        # 生成报告
        report = self.evaluator.generate_report(results, "Stable Cox Equipment Model")
        print(report)

        return results

    def _cross_validate(self, data, duration_col, event_col, weights, cv_folds=5):
        """
        执行交叉验证

        参数:
        - data: 训练数据
        - duration_col: 生存时间列名
        - event_col: 事件指示器列名
        - weights: 样本权重
        - cv_folds: 交叉验证折数

        返回:
        - mean_score: 平均 C-index
        """
        from sklearn.model_selection import KFold

        kf = KFold(n_splits=cv_folds, shuffle=True, random_state=42)
        scores = []

        for train_idx, val_idx in kf.split(data):
            train_fold = data.iloc[train_idx]
            val_fold = data.iloc[val_idx]

            # 训练折叠模型
            fold_model = WeightedCoxModel(**self.config.COX_CONFIG)
            fold_weights = weights[train_idx] if weights is not None else None

            try:
                fold_model.fit(
                    train_fold,
                    duration_col=duration_col,
                    event_col=event_col,
                    weights=fold_weights,
                    feature_names=self.feature_names_
                )

                # 评估
                score = fold_model.score(val_fold, duration_col, event_col)
                scores.append(score)

            except Exception as e:
                logger.warning(f"交叉验证折叠失败: {e}")
                continue

        return np.mean(scores) if scores else 0.0

    def get_feature_importance(self):
        """
        获取特征重要性

        返回:
        - importance: 特征重要性 DataFrame
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练")

        summary = self.cox_model.summary
        importance = pd.DataFrame({
            'feature': summary.index,
            'coefficient': summary['coef'],
            'abs_coefficient': np.abs(summary['coef']),
            'p_value': summary['p'],
            'confidence_interval_lower': summary['coef lower 95%'],
            'confidence_interval_upper': summary['coef upper 95%']
        }).sort_values('abs_coefficient', ascending=False)

        return importance

    def predict_survival_probability(self, X, times):
        """
        预测指定时间点的生存概率

        参数:
        - X: 特征数据
        - times: 时间点列表

        返回:
        - survival_probs: 生存概率矩阵
        """
        if not self.is_fitted:
            raise ValueError("模型尚未训练")

        survival_functions = self.predict(X, return_type='survival_function')

        # 计算指定时间点的生存概率
        survival_probs = []
        for sf in survival_functions:
            probs = [sf(t) for t in times]
            survival_probs.append(probs)

        return np.array(survival_probs)


def main():
    """主函数 - 演示如何使用 Stable Cox 模型"""

    # 示例用法
    print("Stable Cox Equipment Model 演示")
    print("=" * 50)

    # 这里应该替换为您的实际数据路径
    # data_path = "path/to/your/equipment_data.csv"

    # 由于没有实际数据，这里创建一个示例
    print("注意：这是一个演示示例")
    print("请将 data_path 替换为您的实际数据文件路径")

    # 示例配置
    config = Config()

    # 创建模型实例
    model = StableCoxEquipment(config)

    print("\n模型已初始化，准备训练...")
    print("使用方法:")
    print("1. model.fit(data)  # 训练模型")
    print("2. predictions = model.predict(test_data)  # 预测")
    print("3. results = model.evaluate(test_data)  # 评估")

    return model


if __name__ == "__main__":
    main()
