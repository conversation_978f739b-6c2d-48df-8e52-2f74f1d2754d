"""
SRDO + STG 集成模块
实现 SRDO 样本重加权和 STG 特征选择的完整流程
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import math
from sklearn.neural_network import MLPClassifier
from sklearn.linear_model import LogisticRegression
import logging

logger = logging.getLogger(__name__)

class SRDOReweighter:
    """
    SRDO (Sample Reweighting via Density-ratio Optimization) 实现
    通过密度比估计进行样本重加权，消除特征间相关性
    """
    
    def __init__(self, decorrelation_type="global", solver='adam', 
                 hidden_layer_sizes=(100, 5), max_iter=500, random_state=3):
        """
        初始化 SRDO 重加权器
        
        参数:
        - decorrelation_type: 去相关类型 ("global", "group", "category")
        - solver: MLP 求解器
        - hidden_layer_sizes: MLP 隐藏层大小
        - max_iter: 最大迭代次数
        - random_state: 随机种子
        """
        self.decorrelation_type = decorrelation_type
        self.solver = solver
        self.hidden_layer_sizes = hidden_layer_sizes
        self.max_iter = max_iter
        self.random_state = random_state
        self.weights_ = None
        self.original_correlation_ = None
        
    def column_wise_resampling(self, x, sensitive_variables=None):
        """
        对特征列进行随机重采样（列置换）
        
        参数:
        - x: 输入数据 (n_samples, n_features)
        - sensitive_variables: 需要置换的特征索引列表
        
        返回:
        - x_decorrelation: 去相关后的数据
        """
        rng = np.random.RandomState(self.random_state)
        n, p = x.shape
        
        if sensitive_variables is None:
            sensitive_variables = list(range(p))
            
        x_decorrelation = x.copy()
        
        if self.decorrelation_type == "global":
            # 对所有指定特征进行独立置换
            for i in sensitive_variables:
                rand_idx = rng.permutation(n)
                x_decorrelation[:, i] = x[rand_idx, i]
        else:
            raise NotImplementedError(f"decorrelation_type '{self.decorrelation_type}' 暂未实现")
            
        return x_decorrelation
    
    def fit(self, X, sensitive_variables=None):
        """
        拟合 SRDO 重加权器
        
        参数:
        - X: 输入特征数据
        - sensitive_variables: 需要去相关的特征索引
        
        返回:
        - self
        """
        if isinstance(X, pd.DataFrame):
            X = X.values
            
        n, p = X.shape
        logger.info(f"开始 SRDO 算法，样本数: {n}, 特征数: {p}")
        
        # 计算原始相关性
        corr_matrix = np.corrcoef(X, rowvar=False)
        abs_corr_matrix = np.abs(corr_matrix)
        self.original_correlation_ = np.sum(abs_corr_matrix) - np.sum(np.diag(abs_corr_matrix))
        logger.info(f"原始数据总相关性: {self.original_correlation_:.4f}")
        
        # 进行列置换
        X_decorrelation = self.column_wise_resampling(X, sensitive_variables)
        
        # 准备分类数据
        P = pd.DataFrame(X)  # 原始分布
        Q = pd.DataFrame(X_decorrelation)  # 目标分布（去相关）
        
        P['src'] = 1  # 原始分布标签
        Q['src'] = 0  # 目标分布标签
        
        Z = pd.concat([P, Q], ignore_index=True, axis=0)
        labels = Z['src'].values
        Z = Z.drop('src', axis=1).values
        
        # 训练分类器进行密度比估计
        clf = MLPClassifier(
            solver=self.solver,
            hidden_layer_sizes=self.hidden_layer_sizes,
            max_iter=self.max_iter,
            random_state=self.random_state
        )
        
        clf.fit(Z, labels)
        
        # 计算原始数据的概率
        proba = clf.predict_proba(Z)[:len(P), 1]  # P(src=1|x)
        
        # 计算权重: w(x) = q(x)/p(x) = (1-proba)/proba
        weights = (1.0 / proba) - 1.0
        
        # 归一化权重
        weights = weights / np.sum(weights) * len(weights)
        
        self.weights_ = weights.reshape(-1, 1)
        
        logger.info(f"SRDO 完成，权重统计 - 均值: {np.mean(weights):.4f}, "
                   f"标准差: {np.std(weights):.4f}, 最大值: {np.max(weights):.4f}")
        
        return self
    
    def get_weights(self):
        """获取计算的权重"""
        if self.weights_ is None:
            raise ValueError("请先调用 fit 方法")
        return self.weights_


class FeatureSelector(nn.Module):
    """STG 特征选择器"""
    
    def __init__(self, input_dim, sigma=1.0):
        super(FeatureSelector, self).__init__()
        self.mu = torch.nn.Parameter(0.01 * torch.randn(input_dim), requires_grad=True)
        self.sigma = sigma
        self.input_dim = input_dim
        
    def forward(self, x):
        """前向传播"""
        if self.training:
            noise = torch.randn_like(self.mu)
            z = self.mu + self.sigma * noise
        else:
            z = self.mu
            
        stochastic_gate = self.hard_sigmoid(z)
        return x * stochastic_gate
    
    def hard_sigmoid(self, x):
        """硬 sigmoid 激活函数"""
        return torch.clamp(x + 0.5, 0.0, 1.0)
    
    def regularizer(self, x):
        """正则化项"""
        return 0.5 * (1 + torch.erf(x / math.sqrt(2)))
    
    def get_gates(self):
        """获取门控值"""
        return self.mu + 0.5
    
    def get_selection_probabilities(self):
        """获取特征选择概率"""
        return self.regularizer((self.mu + 0.5) / self.sigma)


class LinearRegression(nn.Module):
    """线性回归模型"""
    
    def __init__(self, input_dim, output_dim=1):
        super(LinearRegression, self).__init__()
        self.linear = nn.Linear(input_dim, output_dim, bias=False)
        self.weight_init()
        
    def weight_init(self):
        """权重初始化"""
        torch.nn.init.xavier_uniform_(self.linear.weight)
        
    def forward(self, x):
        return self.linear(x)


class STGFeatureSelector:
    """
    STG (Stochastic Gates) 特征选择器
    结合 SRDO 权重进行加权特征选择
    """
    
    def __init__(self, input_dim, sigma=1.0, lam=0.1, learning_rates=None):
        """
        初始化 STG 特征选择器
        
        参数:
        - input_dim: 输入特征维度
        - sigma: 随机门控的标准差
        - lam: 正则化参数
        - learning_rates: 学习率字典
        """
        self.input_dim = input_dim
        self.sigma = sigma
        self.lam = lam
        
        # 默认学习率
        if learning_rates is None:
            learning_rates = {'model': 1e-3, 'gates': 3e-4}
        self.learning_rates = learning_rates
        
        # 初始化模型组件
        self.feature_selector = FeatureSelector(input_dim, sigma)
        self.regression_model = LinearRegression(input_dim, 1)
        self.loss_fn = nn.MSELoss(reduction='none')
        
        # 优化器
        self.optimizer = optim.Adam([
            {'params': self.regression_model.parameters(), 'lr': learning_rates['model']},
            {'params': self.feature_selector.parameters(), 'lr': learning_rates['gates']}
        ])
        
        self.selected_features_ = None
        self.feature_importance_ = None
        
    def pretrain(self, X, y, epochs=1000):
        """预训练回归模型"""
        X = torch.tensor(X, dtype=torch.float32)
        y = torch.tensor(y, dtype=torch.float32).reshape(-1, 1)
        
        pre_optimizer = optim.Adam(self.regression_model.parameters(), lr=self.learning_rates['model'])
        
        for epoch in range(epochs):
            pre_optimizer.zero_grad()
            pred = self.regression_model(X)
            loss = self.loss_fn(pred, y).mean()
            loss.backward()
            pre_optimizer.step()
            
    def fit(self, X, y, weights=None, epochs=5000, verbose=True):
        """
        训练 STG 特征选择器
        
        参数:
        - X: 特征数据
        - y: 目标变量
        - weights: SRDO 权重
        - epochs: 训练轮数
        - verbose: 是否打印训练信息
        """
        # 转换为 tensor
        X = torch.tensor(X, dtype=torch.float32)
        y = torch.tensor(y, dtype=torch.float32).reshape(-1, 1)
        
        if weights is not None:
            weights = torch.tensor(weights, dtype=torch.float32).reshape(-1, 1)
        else:
            weights = torch.ones_like(y)
            
        # 预训练
        if verbose:
            logger.info("开始预训练回归模型...")
        self.pretrain(X, y, epochs=1000)
        
        # 主训练循环
        if verbose:
            logger.info("开始 STG 特征选择训练...")
            
        for epoch in range(1, epochs + 1):
            self.optimizer.zero_grad()
            
            # 前向传播
            selected_X = self.feature_selector(X)
            pred = self.regression_model(selected_X)
            
            # 计算加权损失
            mse_loss = self.loss_fn(pred, y)
            weighted_loss = torch.sum(weights * mse_loss) / torch.sum(weights)
            
            # 正则化项
            gates = self.feature_selector.get_gates()
            reg_term = torch.sum(self.feature_selector.regularizer((gates) / self.sigma))
            
            # 总损失
            total_loss = weighted_loss + self.lam * reg_term
            
            # 反向传播
            total_loss.backward()
            self.optimizer.step()
            
            # 打印进度
            if verbose and epoch % 1000 == 0:
                selection_probs = self.feature_selector.get_selection_probabilities()
                logger.info(f"Epoch {epoch} | Loss: {total_loss.item():.4f} | "
                           f"Selected features: {torch.sum(selection_probs > 0.5).item()}")
        
        # 确定选择的特征
        selection_probs = self.feature_selector.get_selection_probabilities()
        self.selected_features_ = (selection_probs > 0.5).detach().numpy()
        
        # 计算特征重要性
        gates = self.feature_selector.get_gates().detach().numpy()
        weights_coef = self.regression_model.linear.weight.detach().numpy().flatten()
        self.feature_importance_ = np.abs(gates * weights_coef)
        
        logger.info(f"STG 特征选择完成，选择了 {np.sum(self.selected_features_)} 个特征")
        
        return self
    
    def get_selected_features(self):
        """获取选择的特征索引"""
        if self.selected_features_ is None:
            raise ValueError("请先调用 fit 方法")
        return np.where(self.selected_features_)[0]
    
    def get_feature_importance(self):
        """获取特征重要性"""
        if self.feature_importance_ is None:
            raise ValueError("请先调用 fit 方法")
        return self.feature_importance_


class SRDOSTGPipeline:
    """
    SRDO + STG 完整流水线
    先使用 SRDO 进行样本重加权，再使用 STG 进行特征选择
    """
    
    def __init__(self, srdo_config=None, stg_config=None):
        """
        初始化流水线
        
        参数:
        - srdo_config: SRDO 配置
        - stg_config: STG 配置
        """
        self.srdo_config = srdo_config or {}
        self.stg_config = stg_config or {}
        
        self.srdo_reweighter = None
        self.stg_selector = None
        self.selected_features_ = None
        self.weights_ = None
        
    def fit(self, X, y, feature_names=None):
        """
        拟合完整流水线
        
        参数:
        - X: 特征数据
        - y: 目标变量
        - feature_names: 特征名称列表
        
        返回:
        - self
        """
        if isinstance(X, pd.DataFrame):
            if feature_names is None:
                feature_names = X.columns.tolist()
            X = X.values
            
        # 步骤1: SRDO 样本重加权
        logger.info("步骤1: 应用 SRDO 样本重加权")
        self.srdo_reweighter = SRDOReweighter(**self.srdo_config)
        self.srdo_reweighter.fit(X)
        self.weights_ = self.srdo_reweighter.get_weights()
        
        # 步骤2: STG 特征选择
        logger.info("步骤2: 应用 STG 特征选择")
        self.stg_selector = STGFeatureSelector(X.shape[1], **self.stg_config)
        self.stg_selector.fit(X, y, weights=self.weights_)
        
        # 获取选择的特征
        selected_indices = self.stg_selector.get_selected_features()
        self.selected_features_ = selected_indices
        
        if feature_names is not None:
            selected_names = [feature_names[i] for i in selected_indices]
            logger.info(f"选择的特征: {selected_names}")
        
        return self
    
    def get_selected_features(self):
        """获取选择的特征索引"""
        return self.selected_features_
    
    def get_weights(self):
        """获取 SRDO 权重"""
        return self.weights_
    
    def transform(self, X):
        """转换数据，只保留选择的特征"""
        if self.selected_features_ is None:
            raise ValueError("请先调用 fit 方法")
            
        if isinstance(X, pd.DataFrame):
            return X.iloc[:, self.selected_features_]
        else:
            return X[:, self.selected_features_]
