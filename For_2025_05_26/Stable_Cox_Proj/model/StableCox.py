"""
STG (Stochastic Gates) 特征选择集成模块
直接使用 StableCox-main 中的 STG 实现
"""

import torch
import torch.nn as nn
import math
import torch.optim as optim
import numpy as np
import pandas as pd
import logging
from sklearn.preprocessing import StandardScaler
from For_2025_05_26.Stable_Cox_Proj.model.STG_algorithm import LinearRegression, STG
from For_2025_05_26.Stable_Cox_Proj.model.SRDO_algorithm import SRDOReweighter

logger = logging.getLogger(__name__)

class SRDOSTGFeatureSelector:
    """
    集成 SRDO 和 STG 的特征选择器
    """

    def __init__(self, srdo_config=None, stg_config=None):
        """
        初始化特征选择器

        参数:
        - srdo_config: SRDO 配置
        - stg_config: STG 配置
        """
        self.srdo_config = srdo_config or {}
        self.stg_config = stg_config or {'sigma': 1.0, 'lam': 0.1, 'epochs': 3000}

        self.scaler = StandardScaler()
        self.weights_ = None
        self.selected_features_ = None
        self.feature_names_ = None
        self.stg_model = None
        self.x_decorrelation = None

    def fit(self, X, y, feature_names=None):
        """
        拟合特征选择器

        参数:
        - X: 特征数据
        - y: 目标变量（生存时间）
        - feature_names: 特征名称

        返回:
        - self
        """
        if isinstance(X, pd.DataFrame):
            if feature_names is None:
                feature_names = X.columns.tolist()
            X = X.values

        if isinstance(y, pd.Series):
            y = y.values

        self.feature_names_ = feature_names
        n_samples, n_features = X.shape

        logger.info(f"开始 SRDO + STG 特征选择，样本数: {n_samples}, 特征数: {n_features}")

        # 1. 数据归一化
        logger.info("步骤1: 数据归一化")
        X_normalized = self.scaler.fit_transform(X)

        # 2. SRDO 样本重加权
        logger.info("步骤2: SRDO 样本重加权")

        srdo_reweighter = SRDOReweighter(**self.srdo_config)
        srdo_reweighter.fit(X_normalized)
        self.weights_ = srdo_reweighter.get_weights()  # .flatten()

        # 3. STG 特征选择
        logger.info("步骤3: STG 特征选择")
        self.stg_model = STG(
            input_dim=n_features,
            output_dim=1,
            sigma=self.stg_config.get('sigma', 1.0),
            lam=self.stg_config.get('lam', 0.1)
        )

        # 训练 STG

        X_normalized = np.concatenate((X_normalized, self.weights_), axis=1)
        self.stg_model.train(
            X_normalized,
            y.reshape(-1, 1),
            self.weights_,
            epochs=self.stg_config.get('epochs', 3000)
        )

        # 获取选择的特征
        ratios = self.stg_model.get_ratios().detach().numpy()
        threshold = 0.2  # 选择概率阈值
        self.selected_features_ = ratios > threshold

        selected_indices = np.where(self.selected_features_)[0]
        logger.info(f"STG 选择了 {len(selected_indices)} 个特征")

        if feature_names is not None:
            selected_names = [feature_names[i] for i in selected_indices]
            logger.info(f"选择的特征: {selected_names[:10]}{'...' if len(selected_names) > 10 else ''}")

        return self

    def transform(self, X):
        """
        转换数据，只保留选择的特征并归一化

        参数:
        - X: 输入数据

        返回:
        - X_transformed: 转换后的数据
        """
        if self.selected_features_ is None:
            raise ValueError("请先调用 fit 方法")

        if isinstance(X, pd.DataFrame):
            X = X.values

        # 归一化
        X_normalized = self.scaler.transform(X)

        # 选择特征
        X_selected = X_normalized[:, self.selected_features_]

        return X_selected

    def fit_transform(self, X, y, feature_names=None):
        """拟合并转换数据"""
        return self.fit(X, y, feature_names).transform(X)

    def get_selected_features(self):
        """获取选择的特征索引"""
        if self.selected_features_ is None:
            raise ValueError("请先调用 fit 方法")
        return np.where(self.selected_features_)[0]

    def get_selected_feature_names(self):
        """获取选择的特征名称"""
        if self.feature_names_ is None or self.selected_features_ is None:
            raise ValueError("请先调用 fit 方法并提供 feature_names")
        selected_indices = self.get_selected_features()
        return [self.feature_names_[i] for i in selected_indices]

    def get_weights(self):
        """获取 SRDO 权重"""
        if self.weights_ is None:
            raise ValueError("请先调用 fit 方法")
        return self.weights_

    def get_feature_importance(self):
        """获取特征重要性"""
        if self.stg_model is None:
            raise ValueError("请先调用 fit 方法")

        ratios = self.stg_model.get_ratios().detach().numpy()
        gates = self.stg_model.get_gates().detach().numpy()
        params = self.stg_model.get_params().detach().numpy().flatten()

        importance_df = pd.DataFrame({
            'feature_idx': range(len(ratios)),
            'selection_ratio': ratios,
            'gate_value': gates,
            'coefficient': params,
            'importance_score': ratios * np.abs(params)
        })

        if self.feature_names_ is not None:
            importance_df['feature_name'] = self.feature_names_

        return importance_df.sort_values('importance_score', ascending=False)
