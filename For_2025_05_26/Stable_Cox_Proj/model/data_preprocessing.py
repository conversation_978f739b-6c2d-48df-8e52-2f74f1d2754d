"""
装备材料数据预处理模块
将装备测量数据转换为生存分析格式
基于 a.ipynb 中的实现逻辑
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import logging

logger = logging.getLogger(__name__)

# 导入特征列定义（基于 COL.py）
try:
    from COL import pulse_cols, pretest_cols, burnin_cols, power_on_cols_A, power_on_cols_B
    # 合并所有测量列
    all_measurement_cols = sorted(list(set(
        pulse_cols + power_on_cols_A + power_on_cols_B + burnin_cols + pretest_cols
    )))
except ImportError:
    logger.warning("无法导入 COL.py，将使用默认特征检测")
    all_measurement_cols = []

def is_part_column(col_name):
    """
    判断是否为零件列（基于 a.ipynb 中的实现）
    零件列是在 Time_Series == 1 时所替换的零件列，不用于建模

    参数:
    - col_name: 列名

    返回:
    - bool: 是否为零件列
    """
    protected_cols = [
        'SN_Common', 'Time_Series', 'Date',
        'OVERALL_100V_OPEN_LOOP_TEST_STATUS',
        'OVERALL_200V_OPEN_LOOP_TEST_STATUS',
        'OVERALL_300V_OPEN_LOOP_TEST_STATUS',
        'OVERALL_420V_OPEN_LOOP_TEST_STATUS',
        'PO1 PO Number_ERT'
    ] + all_measurement_cols

    if col_name in protected_cols:
        return False

    # 检查列名是否包含数字（零件列的特征）
    return any(char.isdigit() for char in col_name.replace('-', '').replace('.', ''))

class EquipmentDataProcessor:
    """装备数据预处理器"""

    def __init__(self, config=None):
        """
        初始化数据预处理器

        参数:
        - config: 配置字典
        """
        self.config = config or {}
        self.scaler_ = None
        self.label_encoders_ = {}
        self.feature_names_ = None

    def process_data(self, df, time_col='Time_Series', id_col='SN_Common'):
        """
        处理装备数据，转换为生存分析格式

        参数:
        - df: 原始数据框
        - time_col: 时间列名
        - id_col: 设备ID列名

        返回:
        - processed_data: 处理后的数据框
        """
        logger.info(f"开始处理装备数据，原始数据形状: {df.shape}")

        # 复制数据避免修改原始数据
        data = df.copy()

        # 1. 创建生存时间和事件指示器
        survival_data = self._create_survival_variables(data, time_col, id_col)

        # 2. 处理测量特征
        measurement_cols = self._get_measurement_columns(survival_data)
        processed_features = self._process_features(survival_data, measurement_cols)

        # 3. 移除常数列（方差为0的列）
        processed_features = self._remove_constant_columns(processed_features)

        # 4. 处理缺失值
        processed_data = self._handle_missing_values(processed_features)

        # 5. 数据质量检查
        processed_data = self._quality_check(processed_data)

        logger.info(f"数据处理完成，最终数据形状: {processed_data.shape}")

        return processed_data

    def _create_survival_variables(self, data, time_col, id_col):
        """
        创建生存时间和事件指示器
        基于 a.ipynb 中的实现逻辑：
        - Time_Series == 0: 生产日期
        - Time_Series == 1: 第一次维修日期
        - 生存时间 = 维修日期 - 生产日期

        参数:
        - data: 输入数据
        - time_col: 时间列名 (Time_Series)
        - id_col: 设备ID列名 (SN_Common)

        返回:
        - survival_data: 包含生存变量的数据
        """
        data['Date'] = pd.to_datetime(data['Date'], errors='coerce')
        logger.info("创建生存时间和事件指示器")
        logger.info(f"Time_Series 值分布: {data[time_col].value_counts().sort_index()}")

        # 分别获取生产日期和维修日期的数据
        df_ts0 = data[data[time_col] == 0].copy()  # 生产日期
        df_ts1 = data[data[time_col] == 1].copy()  # 维修日期

        logger.info(f"生产记录数 (Time_Series=0): {len(df_ts0)}")
        logger.info(f"维修记录数 (Time_Series=1): {len(df_ts1)}")

        # 移除零件列（基于 a.ipynb 中的逻辑）
        part_cols = [col for col in data.columns if is_part_column(col)]
        logger.info(f"检测到 {len(part_cols)} 个零件列，将被移除")
        if part_cols:
            logger.info(f"前三个零件列示例: {part_cols[:3]}{'...' if len(part_cols) > 5 else ''}")
            df_ts0.drop(columns=part_cols, inplace=True)

        # 获取所有设备的序列号
        sn_list = data[id_col].unique()
        logger.info(f"总设备数: {len(sn_list)}")

        # 计算生存时间和事件指示器（基于 a.ipynb 中的逻辑）
        survival_data = []
        valid_sn_list = []
        sn_prob = []
        i = 0  # 计数器：维修日期早于生产日期的异常情况
        j = 0  # 计数器：缺少生产日期的情况

        for sn in sn_list:
            df_sn = data[data[id_col] == sn]

            # 获取生产日期和维修日期
            if 'Date' in df_sn.columns:
                start_date = df_sn[df_sn[time_col] == 0]['Date'].min()
                end_date = df_sn[df_sn[time_col] == 1]['Date'].min()
            else:
                raise ValueError("数据中缺少 'Date' 列")

            # 检查生产日期是否缺失
            if pd.isna(start_date):
                j += 1
                continue

            # 计算生存时间和事件指示器
            if pd.isna(end_date):
                # 没有维修记录，认为是删失数据
                duration = (data['Date'].max() - start_date).days
                event_observed = 0
            elif (end_date - start_date).days > 0:
                # 有维修记录且时间合理
                duration = (end_date - start_date).days
                event_observed = 1
            elif (end_date - start_date).days < 0:
                # 维修日期早于生产日期，异常情况
                i += 1
                sn_prob.append(sn)
                continue


            survival_data.append({
                id_col: sn,
                'duration': duration,
                'event': event_observed
            })
            valid_sn_list.append(sn)

        logger.info(f"异常情况统计:")
        logger.info(f"  维修日期早于生产日期: {i} 个设备")
        logger.info(f"  缺少生产日期: {j} 个设备")
        logger.info(f"  有效设备数: {len(valid_sn_list)}")

        # 创建生存数据框
        survival_df = pd.DataFrame(survival_data)
        survival_df = survival_df[~survival_df[id_col].isin(sn_prob)]

        # 合并生产时的测量数据
        df_ts0_filtered = df_ts0[df_ts0[id_col].isin(valid_sn_list)]

        # 合并数据
        df_merged = df_ts0_filtered.merge(survival_df, on=id_col, how='inner')

        # 重命名列以符合生存分析标准
        df_merged = df_merged.rename(columns={'duration': 'survival_time', 'event': 'event'})

        # 删除 Time_Series 列
        if time_col in df_merged.columns:
            df_merged = df_merged.drop(columns=[time_col])

        logger.info(f"最终生存数据形状: {df_merged.shape}")
        logger.info(f"事件率: {df_merged['event'].mean():.3f}")
        logger.info(f"生存时间统计 - 均值: {df_merged['survival_time'].mean():.2f}, "
                   f"中位数: {df_merged['survival_time'].median():.2f}")

        return df_merged



    def _get_measurement_columns(self, data):
        """
        获取测量相关的列
        基于 COL.py 中定义的特征列

        参数:
        - data: 输入数据

        返回:
        - measurement_cols: 测量列列表
        """
        # 如果成功导入了 COL.py 中的特征列定义，优先使用
        if all_measurement_cols:
            # 使用 COL.py 中定义的测量列
            measurement_cols = [col for col in all_measurement_cols if col in data.columns]
            logger.info(f"使用 COL.py 中定义的特征列，找到 {len(measurement_cols)} 个")
        else:
            # 回退到自动检测
            logger.info("使用自动特征检测")
            excluded_cols = {
                'SN_Common', 'Time_Series', 'survival_time', 'event', 'max_time', 'Date',
                'OVERALL_100V_OPEN_LOOP_TEST_STATUS',
                'OVERALL_200V_OPEN_LOOP_TEST_STATUS',
                'OVERALL_300V_OPEN_LOOP_TEST_STATUS',
                'OVERALL_420V_OPEN_LOOP_TEST_STATUS',
                'PO1 PO Number_ERT'
            }

            # 检测测量列：包含数字或特定关键词的列
            measurement_cols = []
            for col in data.columns:
                if col not in excluded_cols and not is_part_column(col):
                    # 检查列名是否包含数字或测量相关关键词
                    if (any(char.isdigit() for char in col.replace('-', '').replace('.', '')) or
                        any(keyword in col.upper() for keyword in
                            ['FLATTOP', 'IERROR', 'CLOSED_LOOP', 'STATUS', 'PULSE', 'V280', 'V700'])):
                        measurement_cols.append(col)

        self.feature_names_ = sorted(measurement_cols)
        logger.info(f"最终检测到 {len(measurement_cols)} 个测量特征")

        return measurement_cols

    def _process_features(self, data, measurement_cols):
        """
        处理特征数据

        参数:
        - data: 输入数据
        - measurement_cols: 测量列列表

        返回:
        - data: 处理后的数据
        """
        logger.info("处理特征数据")

        # 处理数值型特征
        numeric_cols = data[measurement_cols].select_dtypes(include=[np.number]).columns

        # 处理分类型特征
        categorical_cols = data[measurement_cols].select_dtypes(include=['object']).columns

        # 编码分类特征
        for col in categorical_cols:
            if col not in self.label_encoders_:
                self.label_encoders_[col] = LabelEncoder()

            # 处理缺失值
            data[col] = data[col].fillna('missing')
            data[col] = self.label_encoders_[col].fit_transform(data[col].astype(str))

        # 处理数值特征的异常值
        for col in numeric_cols:
            if data[col].dtype in [np.float64, np.int64]:
                # 使用 IQR 方法处理异常值
                Q1 = data[col].quantile(0.25)
                Q3 = data[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                # 截断异常值
                data[col] = data[col].clip(lower_bound, upper_bound)

        return data

    def _remove_constant_columns(self, data):
        """
        移除常数列（方差为0的列）

        参数:
        - data: 输入数据

        返回:
        - data: 移除常数列后的数据
        """
        logger.info("检测和移除常数列")

        # 保护的列（不能删除）
        protected_cols = ['SN_Common', 'survival_time', 'event']

        # 获取数值型列
        numeric_cols = data.select_dtypes(include=[np.number]).columns

        # 检测常数列
        constant_cols = []
        for col in numeric_cols:
            if col not in protected_cols:
                # 计算方差，如果方差为0或接近0，则认为是常数列
                col_var = data[col].var()
                if pd.isna(col_var) or col_var < 1e-10:
                    constant_cols.append(col)
                    logger.info(f"检测到常数列: {col}, 方差: {col_var}")

        # 移除常数列
        if constant_cols:
            data = data.drop(columns=constant_cols)
            logger.info(f"移除了 {len(constant_cols)} 个常数列")
            logger.info(f"移除的常数列: {constant_cols[:10]}{'...' if len(constant_cols) > 10 else ''}")
        else:
            logger.info("未发现常数列")

        return data

    def _handle_missing_values(self, data):
        """
        处理缺失值

        参数:
        - data: 输入数据

        返回:
        - data: 处理缺失值后的数据
        """
        logger.info("处理缺失值")

        # 计算缺失率
        missing_rates = data.isnull().mean()

        # 删除缺失率过高的列（>50%）
        high_missing_cols = missing_rates[missing_rates > 0.5].index
        if len(high_missing_cols) > 0:
            logger.info(f"删除高缺失率列: {list(high_missing_cols)}")
            data = data.drop(columns=high_missing_cols)

        # 对数值列使用中位数填充
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if col not in ['survival_time', 'event', 'Time_Series']:
                data[col] = data[col].fillna(data[col].median())

        # 对分类列使用众数填充
        categorical_cols = data.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            mode_value = data[col].mode()
            if len(mode_value) > 0:
                data[col] = data[col].fillna(mode_value[0])

        return data

    def _quality_check(self, data):
        """
        数据质量检查

        参数:
        - data: 输入数据

        返回:
        - data: 质量检查后的数据
        """
        logger.info("执行数据质量检查")

        # 检查生存时间
        if 'survival_time' in data.columns:
            # 确保生存时间为正数
            data = data[data['survival_time'] > 0]
            logger.info(f"生存时间统计 - 均值: {data['survival_time'].mean():.2f}, "
                       f"中位数: {data['survival_time'].median():.2f}")

        # 检查事件指示器
        if 'event' in data.columns:
            event_rate = data['event'].mean()
            logger.info(f"事件率: {event_rate:.3f}")

            if event_rate < 0.01 or event_rate > 0.99:
                logger.warning(f"事件率异常: {event_rate:.3f}")

        # 删除重复行
        initial_shape = data.shape
        data = data.drop_duplicates()
        if data.shape[0] < initial_shape[0]:
            logger.info(f"删除了 {initial_shape[0] - data.shape[0]} 个重复行")

        # 检查无限值和NaN
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            if np.isinf(data[col]).any():
                logger.warning(f"列 {col} 包含无限值，将被替换")
                data[col] = data[col].replace([np.inf, -np.inf], np.nan)
                data[col] = data[col].fillna(data[col].median())

        return data

    def split_data(self, data, test_size=0.2, random_state=42, stratify_col='event'):
        """
        分割训练集和测试集

        参数:
        - data: 输入数据
        - test_size: 测试集比例
        - random_state: 随机种子
        - stratify_col: 分层列

        返回:
        - train_data, test_data: 训练集和测试集
        """
        logger.info(f"分割数据，测试集比例: {test_size}")

        if stratify_col in data.columns:
            stratify = data[stratify_col]
        else:
            stratify = None

        train_data, test_data = train_test_split(
            data,
            test_size=test_size,
            random_state=random_state,
            stratify=stratify
        )

        logger.info(f"训练集大小: {train_data.shape[0]}, 测试集大小: {test_data.shape[0]}")

        return train_data, test_data
