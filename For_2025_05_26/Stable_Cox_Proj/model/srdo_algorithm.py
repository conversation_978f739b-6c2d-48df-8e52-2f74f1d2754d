"""
SRDO (Sample Reweighting via Density-ratio Optimization) 算法实现
基于 StableCox-main 项目，适配装备材料数据
"""

import numpy as np
import pandas as pd
from sklearn.neural_network import MLPClassifier
from sklearn.linear_model import LogisticRegression
import logging

logger = logging.getLogger(__name__)

def column_wise_resampling(x, p_s, decorrelation_type="global", random_state = 0, category_groups= None,  **options):
    """
    执行列级随机重采样以打破 p(x) 的联合分布
    
    参数:
    - x: 输入特征矩阵 (n_samples, n_features)
    - p_s: 稳定特征的数量
    - decorrelation_type: 去相关类型 ('global' 或 'group')
    - random_state: 随机种子
    - category_groups: 分类特征组
    - options: 其他选项，可包含 'sensitive_variables'
    
    返回:
    - x_decorrelation: 去相关后的特征矩阵
    """
    rng = np.random.RandomState(random_state)
    n, p = x.shape
    if 'sensitive_variables' in options:
        sensitive_variables = options['sensitive_variables']
    else:
        sensitive_variables = [i for i in range(p)]
    x_decorrelation = np.zeros([n, p])
    if decorrelation_type == "global":
        for i in sensitive_variables:
            rand_idx = rng.permutation(n)
            x_decorrelation[:, i] = x[rand_idx, i]
    elif decorrelation_type == "group":
        rand_idx = rng.permutation(n)
        x_decorrelation[:, :p_s] = x[rand_idx, :p_s]
        for i in range(p_s, p):
            rand_idx = rng.permutation(n)
            x_decorrelation[:, i] = x[rand_idx, i]
    elif decorrelation_type == "category":
        print("len", len(category_groups))
        pre_j = 1
        for i, j in enumerate(category_groups):
            if j == pre_j:
                x_decorrelation[:, i] = x[:, i]
                pre_j = j
                continue
            count = category_groups.count(j)
            if count == 1:
                rand_idx = rng.permutation(n)
                x_decorrelation[:, i] = x[rand_idx, i]
            else:
                print(i, count)
                rand_idx = rng.permutation(n)
                x_decorrelation[:, i:i + count] = x[rand_idx, i:i + count]
            pre_j = j

    else:
        assert False
    return x_decorrelation


def SRDO(x, p_s, decorrelation_type="global", solver = 'adam', hidden_layer_sizes = (100, 5), category_groups = None, max_iter = 500, random_state = 3):
    """
    通过密度比估计计算新的样本权重
    
    权重计算公式:
    w(x) = q(x)/p(x) = P(x belongs to q(x) | x) / P(x belongs to p(x) | x)
    
    参数:
    - x: 输入特征矩阵 (n_samples, n_features)
    - p_s: 稳定特征的数量
    - decorrelation_type: 去相关类型
    - solver: MLP 求解器
    - hidden_layer_sizes: MLP 隐藏层大小
    - category_groups: 分类特征组
    - max_iter: 最大迭代次数
    - random_state: 随机种子
    
    返回:
    - weights: 样本权重 (n_samples, 1)
    """
    logger.info(f"开始 SRDO 算法，样本数: {x.shape[0]}, 特征数: {x.shape[1]}")
    
    n, p = x.shape
    
    # 执行列级重采样
    x_decorrelation = column_wise_resampling(x, p_s, decorrelation_type, category_groups = category_groups, random_state = random_state)

    # 创建源分布和目标分布的数据框
    P = pd.DataFrame(x)  # 原始分布
    Q = pd.DataFrame(x_decorrelation)  # 去相关分布
    
    # 计算相关性矩阵用于诊断
    corr_matrix = np.corrcoef(x, rowvar=False)
    abs_corr_matrix = np.abs(corr_matrix)
    sum_abs_corr = np.sum(abs_corr_matrix) - np.sum(np.diag(abs_corr_matrix))
    
    logger.info(f"原始数据总相关性: {sum_abs_corr:.4f}")
    
    # 添加标签：1 表示源分布，0 表示目标分布
    P['src'] = 1
    Q['src'] = 0
    
    # 合并数据
    Z = pd.concat([P, Q], ignore_index=True, axis=0)
    labels = Z['src'].values
    Z = Z.drop('src', axis=1).values
    
    P_data, Q_data = P.values, Q.values
    
    # 训练多层感知机分类器来区分源分布和目标分布
    try:
        if len(hidden_layer_sizes) == 1 and hidden_layer_sizes[0] < 50:
            # 对于小的隐藏层，使用逻辑回归
            clf = LogisticRegression(random_state=random_state, C=0.1, max_iter=max_iter)
        else:
            clf = MLPClassifier(solver=solver, hidden_layer_sizes=hidden_layer_sizes, max_iter=max_iter,
                                random_state=random_state)

        clf.fit(Z, labels)
        
        # 获取属于源分布的概率
        proba = clf.predict_proba(Z)[:len(P_data), 1]

        # 计算样本权重
        # 避免除零错误
        # proba = np.clip(proba, 1e-8, 1-1e-8)
        weights = (1.0 / proba) - 1.0
        
        # 处理异常权重
        # weights = np.clip(weights, 0, np.percentile(weights, 95))
        
        # 归一化权重使其平均值为1
        weights = weights / np.sum(weights)
        weights = np.reshape(weights, [n, 1])

        logger.info(f"SRDO 完成，权重统计 - 均值: {np.mean(weights):.4f}, "
                   f"标准差: {np.std(weights):.4f}, "
                   f"最大值: {np.max(weights):.4f}")
        
        return weights
        
    except Exception as e:
        logger.warning(f"SRDO 算法失败: {e}，返回均匀权重")
        # 如果算法失败，返回均匀权重
        return np.ones((n, 1))


class SRDOReweighter:
    """SRDO 重加权器类"""
    
    def __init__(self, p_s=None, decorrelation_type="global", 
                 solver='adam', hidden_layer_sizes=(256, 64),
                 max_iter=1000, random_state=42):
        """
        初始化 SRDO 重加权器
        
        参数:
        - p_s: 稳定特征数量，如果为 None 则自动估计
        - decorrelation_type: 去相关类型
        - solver: MLP 求解器
        - hidden_layer_sizes: MLP 隐藏层大小
        - max_iter: 最大迭代次数
        - random_state: 随机种子
        """
        self.p_s = p_s
        self.decorrelation_type = decorrelation_type
        self.solver = solver
        self.hidden_layer_sizes = hidden_layer_sizes
        self.max_iter = max_iter
        self.random_state = random_state
        self.weights_ = None
        
    def fit(self, X, feature_groups=None):
        """
        拟合 SRDO 重加权器
        
        参数:
        - X: 特征矩阵
        - feature_groups: 特征分组信息
        
        返回:
        - self
        """
        X = np.array(X)
        n, p = X.shape
        
        # 自动估计稳定特征数量
        if self.p_s is None:
            self.p_s = max(1, p // 2)  # 默认一半特征为稳定特征

        logger.info(f"使用 SRDO 重加权，稳定特征数: {self.p_s}")
        
        # 计算权重
        self.weights_ = SRDO(
            X, self.p_s,
            decorrelation_type=self.decorrelation_type,
            solver=self.solver,
            hidden_layer_sizes=self.hidden_layer_sizes,
            category_groups=feature_groups,
            max_iter=self.max_iter,
            random_state=self.random_state
        )
        
        return self
    
    def get_weights(self):
        """获取计算的权重"""
        if self.weights_ is None:
            raise ValueError("必须先调用 fit 方法")
        return self.weights_
    
    def transform(self, X):
        """返回权重（为了兼容性）"""
        return self.get_weights()
