"""
测试脚本 - 验证 Stable Cox 模型安装和基本功能
"""

import sys
import traceback

def test_imports():
    """测试所有模块导入"""
    print("测试模块导入...")
    
    try:
        import numpy as np
        print("✓ numpy")
    except ImportError as e:
        print(f"✗ numpy: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ pandas")
    except ImportError as e:
        print(f"✗ pandas: {e}")
        return False
    
    try:
        import sklearn
        print("✓ scikit-learn")
    except ImportError as e:
        print(f"✗ scikit-learn: {e}")
        return False
    
    try:
        import lifelines
        print("✓ lifelines")
    except ImportError as e:
        print(f"✗ lifelines: {e}")
        return False
    
    try:
        import matplotlib
        print("✓ matplotlib")
    except ImportError as e:
        print(f"✗ matplotlib: {e}")
        return False
    
    try:
        import seaborn
        print("✓ seaborn")
    except ImportError as e:
        print(f"✗ seaborn: {e}")
        return False
    
    # 测试自定义模块
    try:
        from config import Config
        print("✓ config")
    except ImportError as e:
        print(f"✗ config: {e}")
        return False
    
    try:
        from data_preprocessing import EquipmentDataProcessor
        print("✓ data_preprocessing")
    except ImportError as e:
        print(f"✗ data_preprocessing: {e}")
        return False
    
    try:
        from srdo_algorithm import SRDOReweighter
        print("✓ srdo_algorithm")
    except ImportError as e:
        print(f"✗ srdo_algorithm: {e}")
        return False
    
    try:
        from cox_model import WeightedCoxModel
        print("✓ cox_model")
    except ImportError as e:
        print(f"✗ cox_model: {e}")
        return False
    
    try:
        from evaluation import SurvivalEvaluator
        print("✓ evaluation")
    except ImportError as e:
        print(f"✗ evaluation: {e}")
        return False
    
    try:
        from stable_cox_equipment import StableCoxEquipment
        print("✓ stable_cox_equipment")
    except ImportError as e:
        print(f"✗ stable_cox_equipment: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        import numpy as np
        import pandas as pd
        from stable_cox_equipment import StableCoxEquipment
        from config import Config
        
        # 创建测试数据
        np.random.seed(42)
        n_samples = 100
        
        test_data = {
            'SN_Common': [f'SN_{i:04d}' for i in range(n_samples)],
            'Time_Series': np.random.randint(0, 5, n_samples),
            '0A_Flattop': np.random.normal(100, 10, n_samples),
            '100A_Flattop_Positive': np.random.normal(200, 20, n_samples),
            '100A_IERROR_RMS': np.random.normal(50, 5, n_samples),
            'PULSE_700V_Z2_LOW': np.random.normal(300, 30, n_samples),
            'Z_CLOSED_LOOP_V280_L_X': np.random.normal(280, 15, n_samples)
        }
        
        df = pd.DataFrame(test_data)
        print("✓ 测试数据创建成功")
        
        # 测试配置
        config = Config()
        config.FEATURE_SELECTION_CONFIG['top_n_features'] = 3
        config.TRAINING_CONFIG['use_reweighting'] = False  # 简化测试
        config.EVALUATION_CONFIG['save_plots'] = False
        print("✓ 配置创建成功")
        
        # 测试模型创建
        model = StableCoxEquipment(config)
        print("✓ 模型创建成功")
        
        # 测试数据预处理
        from data_preprocessing import EquipmentDataProcessor
        processor = EquipmentDataProcessor()
        processed_data = processor.process_data(df)
        print("✓ 数据预处理成功")
        
        # 测试模型训练（简化版）
        try:
            model.fit(df)
            print("✓ 模型训练成功")
        except Exception as e:
            print(f"⚠ 模型训练警告: {e}")
            # 训练可能因为数据太小而失败，这是正常的
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("\n测试数据处理功能...")
    
    try:
        import numpy as np
        import pandas as pd
        from data_preprocessing import EquipmentDataProcessor
        
        # 创建更复杂的测试数据
        np.random.seed(42)
        n_samples = 200
        
        test_data = {
            'SN_Common': [f'SN_{i:04d}' for i in range(n_samples)],
            'Time_Series': np.random.randint(0, 10, n_samples),
        }
        
        # 添加多个测量特征
        feature_names = [
            '0A_Flattop', '100A_Flattop_Negative', '100A_Flattop_Positive',
            '100A_IERROR_Peak', '100A_IERROR_RMS', '100V_V280_H_X',
            'PULSE_700V_Z2_LOW', 'Z_CLOSED_LOOP_V280_L_X', 'Z_STATUS_280V_Y1_High'
        ]
        
        for feature in feature_names:
            base_value = np.random.uniform(50, 500)
            test_data[feature] = np.random.normal(base_value, base_value * 0.1, n_samples)
        
        # 添加一些缺失值
        df = pd.DataFrame(test_data)
        df.loc[0:10, '0A_Flattop'] = np.nan
        df.loc[15:20, '100A_IERROR_RMS'] = np.nan
        
        print("✓ 复杂测试数据创建成功")
        
        # 测试数据处理
        processor = EquipmentDataProcessor()
        processed_data = processor.process_data(df)
        
        print(f"✓ 数据处理成功，输出形状: {processed_data.shape}")
        
        # 检查必要列是否存在
        required_cols = ['survival_time', 'event']
        for col in required_cols:
            if col in processed_data.columns:
                print(f"✓ {col} 列存在")
            else:
                print(f"✗ {col} 列缺失")
                return False
        
        # 测试数据分割
        train_data, test_data = processor.split_data(processed_data)
        print(f"✓ 数据分割成功，训练集: {train_data.shape}, 测试集: {test_data.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据处理测试失败: {e}")
        traceback.print_exc()
        return False

def test_algorithms():
    """测试算法组件"""
    print("\n测试算法组件...")
    
    try:
        import numpy as np
        from srdo_algorithm import SRDOReweighter
        from cox_model import WeightedCoxModel
        
        # 创建测试数据
        np.random.seed(42)
        n_samples = 100
        n_features = 5
        
        X = np.random.normal(0, 1, (n_samples, n_features))
        
        # 测试 SRDO
        try:
            reweighter = SRDOReweighter(p_s=3, max_iter=50)  # 减少迭代次数加快测试
            reweighter.fit(X)
            weights = reweighter.get_weights()
            print(f"✓ SRDO 算法测试成功，权重形状: {weights.shape}")
        except Exception as e:
            print(f"⚠ SRDO 算法警告: {e}")
        
        # 测试 Cox 模型组件
        try:
            cox_model = WeightedCoxModel(penalizer=0.1)
            print("✓ Cox 模型创建成功")
        except Exception as e:
            print(f"✗ Cox 模型创建失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 算法组件测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("Stable Cox Equipment Model 安装测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请检查依赖库安装")
        return False
    
    print("\n✅ 所有模块导入成功")
    
    # 测试基本功能
    if not test_basic_functionality():
        print("\n❌ 基本功能测试失败")
        return False
    
    print("\n✅ 基本功能测试通过")
    
    # 测试数据处理
    if not test_data_processing():
        print("\n❌ 数据处理测试失败")
        return False
    
    print("\n✅ 数据处理测试通过")
    
    # 测试算法组件
    if not test_algorithms():
        print("\n❌ 算法组件测试失败")
        return False
    
    print("\n✅ 算法组件测试通过")
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！Stable Cox Equipment Model 安装成功！")
    print("\n下一步：")
    print("1. 运行 'python example_usage.py' 查看完整示例")
    print("2. 使用您的实际数据进行分析")
    print("3. 查看 README.md 了解详细使用方法")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
