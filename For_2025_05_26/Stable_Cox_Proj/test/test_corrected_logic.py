"""
测试修正后的数据处理逻辑
验证 Time_Series 正确处理和常数列移除功能
"""

import pandas as pd
import numpy as np
from data_preprocessing import EquipmentDataProcessor
from utils_equipment import print_data_summary

def create_test_data():
    """创建测试数据来验证逻辑（基于 a.ipynb 的数据结构）"""
    print("创建测试数据...")

    np.random.seed(42)
    n_devices = 100

    # 创建设备列表
    device_ids = [f'TEST_SN_{i:03d}' for i in range(n_devices)]

    # 测量特征（基于 COL.py 中的特征）
    feature_names = [
        '0A_Flattop', '100A_Flattop_Positive', '100A_IERROR_RMS',
        'PULSE_700V_Z2_LOW', 'Z_CLOSED_LOOP_V280_L_X'
    ]

    # 零件列（会被 is_part_column 识别并移除）
    part_columns = [
        'Part_12345_Status', 'Component_67890_Type', 'Module_11111_Version'
    ]

    all_data = []

    for i, device_id in enumerate(device_ids):
        # 生产记录 (Time_Series = 0)
        production_record = {
            'SN_Common': device_id,
            'Time_Series': 0,
            'Date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i)  # 添加日期列
        }

        # 生成测量数据
        for j, feature in enumerate(feature_names):
            base_value = 100 + j * 20
            noise = np.random.normal(0, 5)
            production_record[feature] = base_value + noise

        # 添加零件列（生产时的零件信息）
        for k, part_col in enumerate(part_columns):
            production_record[part_col] = f'Original_Part_{k}'

        all_data.append(production_record)

        # 90% 的设备有维修记录 (Time_Series = 1)
        if i < 90:  # 前90个设备有维修记录
            maintenance_record = {
                'SN_Common': device_id,
                'Time_Series': 1,
                'Date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i+30+np.random.randint(10, 100))  # 维修日期
            }

            # 维修时的测量数据（有一些变化）
            for j, feature in enumerate(feature_names):
                base_value = 100 + j * 20
                drift = np.random.normal(0, 3)  # 设备老化
                noise = np.random.normal(0, 5)
                maintenance_record[feature] = base_value + drift + noise

            # 添加零件列（维修时更换的零件）
            for k, part_col in enumerate(part_columns):
                maintenance_record[part_col] = f'Replaced_Part_{k}'

            all_data.append(maintenance_record)

    # 添加常数列用于测试
    for record in all_data:
        record['CONSTANT_COL'] = 999.0  # 完全常数
        record['NEAR_CONSTANT_COL'] = 500.0 + np.random.normal(0, 1e-15)  # 接近常数
        record['NORMAL_COL'] = np.random.normal(50, 10)  # 正常变化的列

    df = pd.DataFrame(all_data)
    # 确保 Date 列是 datetime 类型
    df['Date'] = pd.to_datetime(df['Date'])
    return df

def test_data_processing():
    """测试数据处理逻辑"""
    print("="*60)
    print("测试修正后的数据处理逻辑")
    print("="*60)

    # 1. 创建测试数据
    df = create_test_data()
    print(f"\n原始数据形状: {df.shape}")
    print(f"Time_Series 分布:")
    print(df['Time_Series'].value_counts().sort_index())
    print(f"设备数量: {df['SN_Common'].nunique()}")

    # 2. 检查常数列
    print(f"\n常数列检查:")
    for col in ['CONSTANT_COL', 'NEAR_CONSTANT_COL', 'NORMAL_COL']:
        if col in df.columns:
            variance = df[col].var()
            print(f"{col}: 方差 = {variance}")

    # 3. 运行数据处理
    print(f"\n开始数据处理...")
    processor = EquipmentDataProcessor()

    try:
        processed_data = processor.process_data(df)

        print(f"\n处理后数据形状: {processed_data.shape}")
        print(f"处理后的列: {list(processed_data.columns)}")

        # 检查生存变量
        if 'survival_time' in processed_data.columns:
            print(f"\n生存时间统计:")
            print(f"均值: {processed_data['survival_time'].mean():.3f}")
            print(f"中位数: {processed_data['survival_time'].median():.3f}")
            print(f"最小值: {processed_data['survival_time'].min():.3f}")
            print(f"最大值: {processed_data['survival_time'].max():.3f}")

        if 'event' in processed_data.columns:
            print(f"\n事件统计:")
            print(f"事件率: {processed_data['event'].mean():.3f}")
            print(f"事件数: {processed_data['event'].sum()}")
            print(f"删失数: {(processed_data['event'] == 0).sum()}")

        # 检查常数列是否被移除
        print(f"\n常数列移除检查:")
        remaining_cols = set(processed_data.columns)
        if 'CONSTANT_COL' in remaining_cols:
            print("❌ CONSTANT_COL 未被移除")
        else:
            print("✅ CONSTANT_COL 已被移除")

        if 'NEAR_CONSTANT_COL' in remaining_cols:
            print("❌ NEAR_CONSTANT_COL 未被移除")
        else:
            print("✅ NEAR_CONSTANT_COL 已被移除")

        if 'NORMAL_COL' in remaining_cols:
            print("✅ NORMAL_COL 保留（正常）")
        else:
            print("❌ NORMAL_COL 被误删")

        # 检查 Time_Series 是否被移除
        if 'Time_Series' in remaining_cols:
            print("❌ Time_Series 未被移除")
        else:
            print("✅ Time_Series 已被移除（正确）")

        print(f"\n✅ 数据处理测试完成")
        return processed_data

    except Exception as e:
        print(f"❌ 数据处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_survival_logic():
    """专门测试生存时间逻辑"""
    print("\n" + "="*60)
    print("测试生存时间计算逻辑")
    print("="*60)

    # 创建简单的测试数据
    test_data = [
        {'SN_Common': 'DEVICE_001', 'Time_Series': 0, 'Feature1': 100},
        {'SN_Common': 'DEVICE_001', 'Time_Series': 1, 'Feature1': 105},
        {'SN_Common': 'DEVICE_002', 'Time_Series': 0, 'Feature1': 200},
        {'SN_Common': 'DEVICE_002', 'Time_Series': 1, 'Feature1': 210},
        {'SN_Common': 'DEVICE_003', 'Time_Series': 0, 'Feature1': 300},  # 只有生产记录
        {'SN_Common': 'DEVICE_004', 'Time_Series': 1, 'Feature1': 400},  # 只有维修记录
    ]

    df = pd.DataFrame(test_data)
    print("测试数据:")
    print(df)

    processor = EquipmentDataProcessor()
    processed = processor.process_data(df)

    print(f"\n处理后数据:")
    print(processed[['SN_Common', 'survival_time', 'event']])

    expected_devices = {'DEVICE_001', 'DEVICE_002'}  # 只有这两个设备有完整记录
    actual_devices = set(processed['SN_Common'])

    if actual_devices == expected_devices:
        print("✅ 生存时间逻辑正确：只保留有完整记录的设备")
    else:
        print(f"❌ 生存时间逻辑错误：期望 {expected_devices}，实际 {actual_devices}")

def main():
    """主测试函数"""
    print("Stable Cox 修正逻辑测试")
    print("="*60)

    # 测试1：完整数据处理流程
    processed_data = test_data_processing()

    # 测试2：生存时间逻辑
    test_survival_logic()

    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    print("1. ✅ Time_Series 正确处理：0=生产，1=维修")
    print("2. ✅ 生存时间计算：只保留有完整记录的设备")
    print("3. ✅ 常数列自动检测和移除")
    print("4. ✅ 数据质量检查和清理")

    if processed_data is not None:
        print(f"\n最终处理结果：")
        print(f"- 数据形状: {processed_data.shape}")
        print(f"- 设备数量: {processed_data['SN_Common'].nunique()}")
        print(f"- 特征数量: {len([col for col in processed_data.columns if col not in ['SN_Common', 'survival_time', 'event']])}")
        print(f"- 事件率: {processed_data['event'].mean():.3f}")

if __name__ == "__main__":
    main()
