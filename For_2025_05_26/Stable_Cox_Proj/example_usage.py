"""
Stable Cox 模型使用示例
演示如何将模型应用到装备材料测量数据
按照 a.ipynb 中的逻辑和 StableCox 原理进行数据处理和建模

修改内容：
1. 根据 a.ipynb 的格式创建过程数据
2. 应用事件过滤逻辑：event_0_mask | event_1_with_part_mask
3. 保存数据到 ./Stable_Cox_Proj/data/ 目录
4. 使用 StableCox 自动 S/V 特征选择
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from stable_cox_equipment import StableCoxEquipment
from config import Config
from utils_equipment import print_data_summary

from For_2025_05_26.Stable_Cox_Proj.model.data_preprocessing import is_part_column
from For_2025_05_26.Stable_Cox_Proj.model.data_preprocessing import EquipmentDataProcessor

import warnings
warnings.filterwarnings('ignore')

# 导入特征列定义
try:
    from For_2025_05_26.Stable_Cox_Proj.model.COL import pulse_cols, pretest_cols, burnin_cols, power_on_cols_A, power_on_cols_B
    all_measurement_cols = sorted(list(set(
        pulse_cols + power_on_cols_A + power_on_cols_B + burnin_cols + pretest_cols
    )))
    print(f"成功导入 COL.py，共 {len(all_measurement_cols)} 个测量特征")
except ImportError:
    print("警告：无法导入 COL.py，将使用默认特征检测")
    all_measurement_cols = []

class EquipmentDataManager:
    """
    装备数据管理器
    按照 a.ipynb 中的逻辑处理数据，创建 ts0, ts1 数据
    """

    def __init__(self):
        self.data_dict = {}  # 保存各种数据框的字典
        self.feature_groups = {}  # 特征分组

    def load_data(self, data_path=None):
        """加载装备数据"""
        if data_path is None:
            # 尝试从默认路径加载
            try:
                MAIN_PATH = r"C:\Users\<USER>\OneDrive - CentraleSupelec\PY_code"
                data_path = fr"{MAIN_PATH}\Proj_SSSA_SSSPS\Peikai\Data\Final_Data\SSSA_filled.csv"
                df = pd.read_csv(data_path)
                print(f"✅ 成功加载真实数据，形状: {df.shape}")
                self.data_dict['raw_data'] = df
                return df
            except Exception as e:
                print(f"⚠️ 无法加载真实数据: {e}")
                print("使用演示数据...")
                return self.create_demo_data()
        else:
            try:
                df = pd.read_csv(data_path)
                print(f"✅ 成功加载数据，形状: {df.shape}")
                self.data_dict['raw_data'] = df
                return df
            except Exception as e:
                print(f"❌ 数据加载失败: {e}")
                return None

    def create_demo_data(self):
        """创建演示数据（基于 a.ipynb 的数据结构）"""
        print("创建演示数据...")

        np.random.seed(42)
        n_devices = 300

        # 使用 COL.py 中的特征或默认特征
        if all_measurement_cols:
            measurement_features = all_measurement_cols[:20]  # 使用前20个特征
        else:
            measurement_features = [
                '0A_Flattop', '100A_Flattop_Negative', '100A_Flattop_Positive',
                '100A_IERROR_Peak', '100A_IERROR_RMS', '100V_V280_H_X', '100V_V280_H_Y',
                '100V_V280_H_Z', '100V_V280_L_X', '100V_V280_L_Y', 'PULSE_700V_Z2_LOW',
                'Z_CLOSED_LOOP_V280_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'Z_CLOSED_LOOP_V280_L_Z',
                'Z_CLOSED_LOOP_V700_H_X', 'Z_CLOSED_LOOP_V700_H_Y', 'Z_CLOSED_LOOP_V700_H_Z',
                'Z_CLOSED_LOOP_V700_L_X', 'Z_CLOSED_LOOP_V700_L_Y', 'Z_STATUS_280V_Y1_High'
            ]

        # 零件列（会被识别并移除）
        part_columns = [
            'Part_12345_Status', 'Component_67890_Type', 'Module_11111_Version'
        ]

        # 状态列
        status_columns = [
            'OVERALL_100V_OPEN_LOOP_TEST_STATUS',
            'OVERALL_200V_OPEN_LOOP_TEST_STATUS',
            'OVERALL_300V_OPEN_LOOP_TEST_STATUS',
            'OVERALL_420V_OPEN_LOOP_TEST_STATUS'
        ]

        all_data = []
        device_ids = [f'SN_{i:04d}' for i in range(n_devices)]

        for i, device_id in enumerate(device_ids):
            # 生产记录 (Time_Series = 0)
            production_record = {
                'SN_Common': device_id,
                'Time_Series': 0,
                'Date': pd.Timestamp('2020-01-01') + pd.Timedelta(days=i),
                'PO1 PO Number_ERT': f'PO_{i:06d}'
            }

            # 生成测量数据
            for j, feature in enumerate(measurement_features):
                base_value = 100 + j * 15 + np.random.normal(0, 3)
                production_record[feature] = base_value

            # 添加零件列
            for k, part_col in enumerate(part_columns):
                production_record[part_col] = f'Original_Part_{k}_{i}'

            # 添加状态列
            for status_col in status_columns:
                production_record[status_col] = np.random.choice(['PASS', 'FAIL'], p=[0.95, 0.05])

            all_data.append(production_record)

            # 80% 的设备有维修记录 (Time_Series = 1)
            if i < int(n_devices * 0.8):
                maintenance_days = np.random.randint(30, 400)
                maintenance_record = {
                    'SN_Common': device_id,
                    'Time_Series': 1,
                    'Date': production_record['Date'] + pd.Timedelta(days=maintenance_days),
                    'PO1 PO Number_ERT': production_record['PO1 PO Number_ERT']
                }

                # 维修时的测量数据（有设备老化效应）
                for j, feature in enumerate(measurement_features):
                    base_value = 100 + j * 15
                    aging_effect = np.random.normal(0, 8)  # 老化效应
                    noise = np.random.normal(0, 4)
                    maintenance_record[feature] = base_value + aging_effect + noise

                # 维修时更换的零件
                for k, part_col in enumerate(part_columns):
                    maintenance_record[part_col] = f'Replaced_Part_{k}_{i}'

                # 维修后的状态
                for status_col in status_columns:
                    maintenance_record[status_col] = np.random.choice(['PASS', 'FAIL'], p=[0.85, 0.15])

                all_data.append(maintenance_record)

        # 添加一些常数列用于测试
        for record in all_data:
            record['CONSTANT_COL_1'] = 999.0
            record['CONSTANT_COL_2'] = 500.0
            record['NEAR_CONSTANT_COL'] = 100.0 + np.random.normal(0, 1e-12)

        df = pd.DataFrame(all_data)
        df['Date'] = pd.to_datetime(df['Date'])

        print(f"演示数据创建完成:")
        print(f"  数据形状: {df.shape}")
        print(f"  Time_Series 分布: {df['Time_Series'].value_counts().sort_index()}")
        print(f"  设备数量: {df['SN_Common'].nunique()}")

        self.data_dict['raw_data'] = df
        return df

    def preprocess_data(self, df=None):
        """
        按照 a.ipynb 中的逻辑预处理数据
        创建 ts0, ts1 数据和生存分析数据
        """
        if df is None:
            df = self.data_dict.get('raw_data')
            if df is None:
                raise ValueError("没有可用的数据，请先加载数据")

        print("\n" + "="*60)
        print("开始数据预处理（按照 a.ipynb 逻辑）")
        print("="*60)

        # 1. 创建 ts0 和 ts1 数据
        print("1. 创建 ts0 (生产数据) 和 ts1 (维修数据)")
        df_ts0 = df[df['Time_Series'] == 0].copy()
        df_ts1 = df[df['Time_Series'] == 1].copy()

        print(f"   ts0 数据形状: {df_ts0.shape}")
        print(f"   ts1 数据形状: {df_ts1.shape}")

        # 2. 移除零件列（基于 a.ipynb 中的 is_part_column 逻辑）
        print("2. 移除零件列")
        part_cols = [col for col in df_ts0.columns if is_part_column(col)]
        print(f"   检测到 {len(part_cols)} 个零件列")
        if part_cols:
            print(f"   零件列示例: {part_cols[:3]}{'...' if len(part_cols) > 3 else ''}")

        # 3. 计算生存时间和事件指示器
        print("3. 计算生存时间和事件指示器")
        sn_list = df['SN_Common'].unique()
        survival_data = []
        i = j = 0
        sn_prob = []
        valid_sn_list = []
        for sn in sn_list:
            df_sn = df[df['SN_Common'] == sn]
            start_date = df_sn[df_sn['Time_Series'] == 0]['Date'].min()
            end_date = df_sn[df_sn['Time_Series'] == 1]['Date'].min()

            if pd.isna(start_date):
                j += 1
                continue

            if pd.isna(end_date):
                duration = (df['Date'].max() - start_date).days
                event_observed = 0
            elif (end_date - start_date).days > 0:
                duration = (end_date - start_date).days
                event_observed = 1
            elif (end_date - start_date).days < 0:
                i += 1
                sn_prob.append(sn)
                continue

            survival_data.append({'SN_Common': sn, 'duration': duration, 'event': event_observed})
            valid_sn_list.append(sn)

        print(f"   异常情况统计:")
        print(f"     维修日期早于生产日期: {i} 个设备")
        print(f"     缺少生产日期: {j} 个设备")
        print(f"     有效设备数: {len(valid_sn_list)}")

        # 4. 创建最终的建模数据（类似 a.ipynb 中的 df_merged1）
        print("4. 创建最终建模数据")
        df_survival = pd.DataFrame(survival_data)
        df_survival = df_survival[~df_survival['SN_Common'].isin(sn_prob)]
        df_ts0 = df[
            (df['SN_Common'].isin(valid_sn_list)) &
            (df['Time_Series'] == 0)
            ]
        df_ts0 = df_ts0.sort_values(by=['SN_Common', 'Date']).drop_duplicates(subset=['SN_Common'])
        df_ts0.drop(columns=part_cols, inplace=True)
        df_ts0_model = df_ts0[['SN_Common'] + all_measurement_cols].copy()

        # 合并数据
        df_ts1 = df[df['Time_Series'] == 1][['SN_Common'] + part_cols]
        df_merged = df_ts0_model.merge(df_ts1, how='left', left_on='SN_Common', right_on='SN_Common')
        df_merged[part_cols] = df_merged[part_cols].fillna(0)
        df_merged = pd.merge(df_merged, df_survival, on='SN_Common', how='left')
        df_merged = df_merged.rename(columns={'duration': 'survival_time', 'event': 'event'})

        # 移除 Time_Series 列
        if 'Time_Series' in df_merged.columns:
            df_merged = df_merged.drop(columns=['Time_Series'])

        # 5. 应用事件过滤逻辑（按照您的要求）
        print("5. 应用事件过滤逻辑")
        print(f"   过滤前数据形状: {df_merged.shape}")

        # 获取 part_cols（零件列）
        print(f"   检测到 {len(part_cols)} 个零件列用于过滤")

        # 应用过滤逻辑：event_0_mask | event_1_with_part_mask
        event_0_mask = df_merged['event'] == 0
        event_1_with_part_mask = (df_merged['event'] == 1) & (df_merged[part_cols].sum(axis=1) != 0)

        final_mask = event_0_mask | event_1_with_part_mask
        df_merged1 = df_merged[final_mask].copy()

        # 移除零件列（因为它们不用于建模）
        if part_cols:
            df_merged1.drop(columns=part_cols, inplace=True)
            print(f"   移除 {len(part_cols)} 个零件列")

        print(f"   过滤后数据形状: {df_merged1.shape}")
        print(f"   事件率: {df_merged1['event'].mean():.3f}")
        print(f"   生存时间统计 - 均值: {df_merged1['survival_time'].mean():.1f} 天")

        # 保存到数据字典
        self.data_dict['df_ts0'] = df_ts0
        self.data_dict['df_ts1'] = df_ts1
        self.data_dict['survival_data'] = df_survival
        self.data_dict['df_merged'] = df_merged
        self.data_dict['df_merged1'] = df_merged1  # 过滤后的最终数据
        self.data_dict['valid_sn_list'] = valid_sn_list
        self.data_dict['part_cols'] = part_cols

        return df_merged1

    def auto_feature_grouping(self, df_merged=None):
        """
        自动特征分组：S（稳定特征）和 V（不稳定特征）
        按照 StableCox 的原理进行特征分组
        """
        if df_merged is None:
            df_merged = self.data_dict.get('df_merged')
            if df_merged is None:
                raise ValueError("没有可用的建模数据，请先进行数据预处理")

        print("\n" + "="*60)
        print("自动特征分组（StableCox S/V 分组）")
        print("="*60)

        # 获取所有特征列（排除标识和目标列）
        excluded_cols = {'SN_Common', 'survival_time', 'event', 'Date', 'PO1 PO Number_ERT'}
        all_cols = [col for col in df_merged.columns if col not in excluded_cols]

        # 只保留数值型特征列
        feature_cols = []
        for col in all_cols:
            if pd.api.types.is_numeric_dtype(df_merged[col]):
                feature_cols.append(col)
            else:
                print(f"   跳过非数值列: {col} (类型: {df_merged[col].dtype})")

        print(f"数值型特征列: {len(feature_cols)} 个")

        # 移除常数列
        constant_cols = []
        for col in feature_cols:
            try:
                col_var = df_merged[col].var()
                if pd.isna(col_var) or col_var < 1e-10:
                    constant_cols.append(col)
            except Exception as e:
                print(f"   警告：列 {col} 方差计算失败: {e}")
                constant_cols.append(col)

        if constant_cols:
            print(f"移除 {len(constant_cols)} 个常数列: {constant_cols[:3]}{'...' if len(constant_cols) > 3 else ''}")
            feature_cols = [col for col in feature_cols if col not in constant_cols]

        print(f"可用特征数量: {len(feature_cols)}")

        # 按照 StableCox 原理进行特征分组
        # S 特征：稳定特征（通常是基础测量特征）
        # V 特征：不稳定特征（通常是复杂计算特征或状态特征）

        s_features = []  # 稳定特征
        v_features = []  # 不稳定特征

        for feature in feature_cols:
            feature_upper = feature.upper()

            # S 特征：基础测量特征（更稳定）
            if any(keyword in feature_upper for keyword in [
                'FLATTOP', 'IERROR', 'V280', 'V700', 'PULSE'
            ]):
                s_features.append(feature)
            # V 特征：状态和复杂特征（更不稳定）
            elif any(keyword in feature_upper for keyword in [
                'STATUS', 'CLOSED_LOOP', 'OVERALL', 'TEST'
            ]):
                v_features.append(feature)
            else:
                # 默认分配到 V 特征
                v_features.append(feature)

        # 确保 S 和 V 特征数量合理
        total_features = len(s_features) + len(v_features)
        if len(s_features) < total_features * 0.3:
            # 如果 S 特征太少，从 V 中移一些过来
            move_count = int(total_features * 0.4) - len(s_features)
            if move_count > 0 and move_count <= len(v_features):
                s_features.extend(v_features[:move_count])
                v_features = v_features[move_count:]

        print(f"S 特征（稳定特征）: {len(s_features)} 个")
        print(f"  示例: {s_features[:3]}{'...' if len(s_features) > 3 else ''}")
        print(f"V 特征（不稳定特征）: {len(v_features)} 个")
        print(f"  示例: {v_features[:3]}{'...' if len(v_features) > 3 else ''}")

        # 保存特征分组
        self.feature_groups = {
            'S_features': s_features,
            'V_features': v_features,
            'all_features': feature_cols,
            'constant_cols': constant_cols
        }

        return self.feature_groups

    def save_processed_data(self, output_dir="./data/"):
        """
        保存处理后的数据到指定目录
        包含建模所需的所有列：SN_Common, all_measurement_cols, duration, event
        """
        print("\n" + "="*60)
        print("保存处理后的数据")
        print("="*60)

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        print(f"输出目录: {output_dir}")

        # 获取处理后的数据
        df_merged1 = self.data_dict.get('df_merged1')
        if df_merged1 is None:
            raise ValueError("没有可用的处理后数据，请先运行 preprocess_data")

        # 确保包含建模所需的列
        required_cols = ['SN_Common', 'survival_time', 'event']
        missing_cols = [col for col in required_cols if col not in df_merged1.columns]
        if missing_cols:
            raise ValueError(f"缺少必需的列: {missing_cols}")

        # 获取测量列
        measurement_cols = []
        excluded_cols = {'SN_Common', 'survival_time', 'event', 'Date', 'PO1 PO Number_ERT'}
        for col in df_merged1.columns:
            if col not in excluded_cols and pd.api.types.is_numeric_dtype(df_merged1[col]):
                measurement_cols.append(col)

        print(f"保存的数据包含:")
        print(f"  - 样本数: {len(df_merged1)}")
        print(f"  - 测量特征数: {len(measurement_cols)}")
        print(f"  - 事件率: {df_merged1['event'].mean():.3f}")

        # 重命名 survival_time 为 duration（按照您的要求）
        df_to_save = df_merged1.copy()
        df_to_save = df_to_save.rename(columns={'survival_time': 'duration'})

        # 保存主要数据文件
        main_file = os.path.join(output_dir, "processed_equipment_data.csv")
        df_to_save.to_csv(main_file, index=False)
        print(f"✓ 主要数据已保存: {main_file}")

        # 保存特征列表
        feature_info = {
            'all_measurement_cols': measurement_cols,
            'total_features': len(measurement_cols),
            'sample_count': len(df_to_save),
            'event_rate': df_to_save['event'].mean()
        }

        import json
        feature_file = os.path.join(output_dir, "feature_info.json")
        with open(feature_file, 'w', encoding='utf-8') as f:
            json.dump(feature_info, f, indent=2, ensure_ascii=False)
        print(f"✓ 特征信息已保存: {feature_file}")

        # 保存中间数据（可选）
        if 'df_ts0' in self.data_dict:
            ts0_file = os.path.join(output_dir, "ts0_production_data.csv")
            self.data_dict['df_ts0'].to_csv(ts0_file, index=False)
            print(f"✓ 生产数据已保存: {ts0_file}")

        if 'df_ts1' in self.data_dict:
            ts1_file = os.path.join(output_dir, "ts1_maintenance_data.csv")
            self.data_dict['df_ts1'].to_csv(ts1_file, index=False)
            print(f"✓ 维修数据已保存: {ts1_file}")

        print(f"\n所有数据已保存到: {output_dir}")
        return main_file

def run_stable_cox_with_saved_data():
    """
    使用保存的数据运行 Stable Cox 分析
    使用 SRDO + STG 进行自动特征选择和样本重加权
    """
    print("\n" + "="*60)
    print("开始 Stable Cox 分析（使用保存的数据和 SRDO + STG）")
    print("="*60)

    # 1. 加载保存的数据
    print("1. 加载保存的数据...")
    data_file = "./data/processed_equipment_data.csv"

    try:
        df_merged1 = pd.read_csv(data_file)
        print(f"✓ 成功加载来自{data_file}的数据，形状: {df_merged1.shape}")
    except FileNotFoundError:
        print(f"❌ 数据文件未找到: {data_file}")
        print("请先运行数据预处理步骤")
        return None

    # 数据概览
    print_data_summary(df_merged1, "加载的建模数据")

    # 2. 准备特征和目标变量
    print("\n2. 准备特征和目标变量...")
    excluded_cols = {'SN_Common', 'duration', 'event', 'Date', 'PO1 PO Number_ERT'}

    # 获取所有数值特征列
    feature_cols = all_measurement_cols
    # for col in df_merged1.columns:
    #     if col not in excluded_cols and pd.api.types.is_numeric_dtype(df_merged1[col]):
    #         # 检查是否为常数列
    #         try:
    #             col_var = df_merged1[col].var()
    #             if not (pd.isna(col_var) or col_var < 1e-10):
    #                 feature_cols.append(col)
    #         except Exception:
    #             continue

    print(f"可用特征数: {len(feature_cols)}")

    # 准备数据
    X = df_merged1[feature_cols]  # 填充缺失值
    y = df_merged1['duration']  # 生存时间
    event = df_merged1['event']  # 事件指示器

    print(f"特征矩阵形状: {X.shape}")
    print(f"目标变量形状: {y.shape}")
    print(f"事件率: {event.mean():.3f}")

    # 3. SRDO + STG 特征选择
    print("\n3. 应用 SRDO + STG 特征选择...")
    from stg_integration import SRDOSTGFeatureSelector

    # 配置 SRDO 和 STG
    srdo_config = {
        'decorrelation_type': 'global',
        'max_iter': 1000,
        'random_state': 42
    }

    stg_config = {
        'sigma': 1.0,
        'lam': 0.1,
        'epochs': 3000
    }

    # 创建特征选择器
    feature_selector = SRDOSTGFeatureSelector(
        srdo_config=srdo_config,
        stg_config=stg_config
    )

    try:
        # 拟合特征选择器
        feature_selector.fit(X, y, feature_names=feature_cols)

        # 获取选择的特征
        selected_features = feature_selector.get_selected_feature_names()
        weights = feature_selector.get_weights()

        print(f"✓ 特征选择完成")
        print(f"  - 选择了 {len(selected_features)} 个特征")
        print(f"  - 权重统计: 均值={np.mean(weights):.4f}, 标准差={np.std(weights):.4f}")
        print(f"  - 前10个选择的特征: {selected_features[:10]}")

        # 4. 使用选择的特征进行 Cox 建模
        print("\n4. 使用选择的特征进行 Cox 建模...")

        # 准备建模数据
        X_selected = feature_selector.transform(X)
        modeling_data = pd.DataFrame(X_selected, columns=selected_features)
        modeling_data['SN_Common'] = df_merged1['SN_Common'].values
        modeling_data['survival_time'] = y.values
        modeling_data['event'] = event.values

        # 创建 StableCox 模型
        config = Config()
        config.FEATURE_SELECTION_CONFIG['use_feature_selection'] = False  # 已经选择过特征
        config.TRAINING_CONFIG['use_reweighting'] = False  # 已经通过 SRDO 处理过
        config.TRAINING_CONFIG['normalize_features'] = False  # 已经归一化

        model = StableCoxEquipment(config)

        # 训练模型（数据已经预处理过）
        model.fit(
            modeling_data,
            duration_col='survival_time',
            event_col='event',
            feature_cols=selected_features
        )

        print("✓ StableCox 模型训练完成")

        return model, feature_selector, modeling_data

    except Exception as e:
        print(f"✗ 特征选择或建模失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_demo_data():
    """
    创建演示数据（如果无法加载真实数据）
    模拟正确的 Time_Series 逻辑：0=生产日期，1=维修日期
    """
    print("创建演示数据...")

    np.random.seed(42)
    n_devices = 500  # 设备数量

    # 创建设备列表
    device_ids = [f'SN_{i:04d}' for i in range(n_devices)]

    # 创建模拟的测量特征名称
    feature_names = [
        '0A_Flattop', '100A_Flattop_Negative', '100A_Flattop_Positive',
        '100A_IERROR_Peak', '100A_IERROR_RMS', '100V_V280_H_X', '100V_V280_H_Y',
        '100V_V280_H_Z', '100V_V280_L_X', '100V_V280_L_Y', 'PULSE_700V_Z2_LOW',
        'Z_CLOSED_LOOP_V280_L_X', 'Z_CLOSED_LOOP_V280_L_Y', 'Z_CLOSED_LOOP_V280_L_Z',
        'Z_CLOSED_LOOP_V700_H_X', 'Z_CLOSED_LOOP_V700_H_Y', 'Z_CLOSED_LOOP_V700_H_Z',
        'Z_CLOSED_LOOP_V700_L_X', 'Z_CLOSED_LOOP_V700_L_Y', 'Z_STATUS_280V_Y1_High'
    ]

    all_data = []

    for device_id in device_ids:
        # 为每个设备创建生产记录 (Time_Series = 0)
        production_record = {'SN_Common': device_id, 'Time_Series': 0}

        # 生成生产时的测量数据
        for i, feature in enumerate(feature_names):
            base_value = 100 + i * 10
            noise = np.random.normal(0, 5)
            production_record[feature] = base_value + noise

        all_data.append(production_record)

        # 80% 的设备有维修记录 (Time_Series = 1)
        if np.random.random() < 0.8:
            maintenance_record = {'SN_Common': device_id, 'Time_Series': 1}

            # 生成维修时的测量数据（通常会有一些变化）
            for i, feature in enumerate(feature_names):
                base_value = 100 + i * 10
                # 维修时的数据可能有所不同
                drift = np.random.normal(0, 2)  # 设备老化导致的漂移
                noise = np.random.normal(0, 5)
                maintenance_record[feature] = base_value + drift + noise

            all_data.append(maintenance_record)

    # 添加一些常数列用于测试常数列移除功能
    for record in all_data:
        record['CONSTANT_COL_1'] = 100.0  # 完全常数
        record['CONSTANT_COL_2'] = 200.0  # 完全常数
        record['NEAR_CONSTANT_COL'] = 50.0 + np.random.normal(0, 1e-12)  # 接近常数

    df = pd.DataFrame(all_data)
    print(f"演示数据创建完成，形状: {df.shape}")
    print(f"Time_Series 分布: {df['Time_Series'].value_counts().sort_index()}")
    print(f"设备数量: {df['SN_Common'].nunique()}")

    return df

def run_stable_cox_analysis(df_merged1):
    """
    运行 Stable Cox 分析
    使用 StableCox 的自动 S/V 特征选择，不进行手动特征选择
    """
    print("\n" + "="*60)
    print("开始 Stable Cox 分析（使用自动特征选择）")
    print("="*60)

    # 1. 数据概览
    print_data_summary(df_merged1, "处理后的建模数据")

    # 2. 移除常数列
    print("\n移除常数列...")
    excluded_cols = {'SN_Common', 'duration', 'event', 'Date', 'PO1 PO Number_ERT'}
    feature_cols = [col for col in df_merged1.columns if col not in excluded_cols]

    # 检测常数列
    constant_cols = []
    for col in feature_cols:
        if pd.api.types.is_numeric_dtype(df_merged1[col]):
            try:
                col_var = df_merged1[col].var()
                if pd.isna(col_var) or col_var < 1e-10:
                    constant_cols.append(col)
            except Exception:
                constant_cols.append(col)

    if constant_cols:
        print(f"移除 {len(constant_cols)} 个常数列: {constant_cols[:3]}{'...' if len(constant_cols) > 3 else ''}")
        df_modeling = df_merged1.drop(columns=constant_cols)
    else:
        df_modeling = df_merged1.copy()
        print("未发现常数列")

    # 获取最终的测量特征列
    final_feature_cols = [col for col in df_modeling.columns if col not in excluded_cols]
    print(f"最终用于建模的特征数: {len(final_feature_cols)}")

    # 3. 配置模型（使用 SRDO + STG 自动特征选择）
    config = Config()

    # 使用 SRDO + STG 进行自动特征选择和样本重加权
    config.FEATURE_SELECTION_CONFIG['method'] = 'srdo_stg'    # 使用 SRDO + STG
    config.FEATURE_SELECTION_CONFIG['use_feature_selection'] = True
    config.TRAINING_CONFIG['use_reweighting'] = True          # 使用 SRDO 重加权
    config.TRAINING_CONFIG['normalize_features'] = True       # 特征归一化
    config.EVALUATION_CONFIG['save_plots'] = True
    config.SRDO_CONFIG['max_iter'] = 300                      # SRDO 迭代次数
    config.STG_CONFIG = {                                     # STG 配置
        'sigma': 1.0,
        'lam': 0.1,
        'epochs': 3000,
        'learning_rates': {'model': 1e-3, 'gates': 3e-4}
    }

    print(f"配置信息:")
    print(f"  - 特征选择方法: SRDO + STG (自动特征选择)")
    print(f"  - 使用 SRDO 重加权: {config.TRAINING_CONFIG['use_reweighting']}")
    print(f"  - 特征归一化: {config.TRAINING_CONFIG['normalize_features']}")
    print(f"  - SRDO 最大迭代次数: {config.SRDO_CONFIG['max_iter']}")
    print(f"  - STG 训练轮数: {config.STG_CONFIG['epochs']}")

    # 4. 创建并训练模型
    print("\n创建 Stable Cox 模型...")
    model = StableCoxEquipment(config)

    print("开始训练模型...")
    try:
        # 重命名列以符合模型期望
        df_for_model = df_modeling.rename(columns={'duration': 'survival_time'})
        model.fit(df_for_model)
        print("✓ 模型训练完成")
    except Exception as e:
        print(f"✗ 模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

    # 5. 数据分割用于评估
    print("\n分割数据用于评估...")
    from sklearn.model_selection import train_test_split

    train_data, test_data = train_test_split(
        df_for_model, test_size=0.3, random_state=42,
        stratify=df_for_model['event']
    )
    print(f"训练集: {train_data.shape[0]} 样本")
    print(f"测试集: {test_data.shape[0]} 样本")

    # 6. 模型评估
    print("\n评估模型性能...")
    try:
        results = model.evaluate(test_data)
        print("✓ 模型评估完成")

        # 打印关键指标
        print(f"\n关键性能指标:")
        print(f"C-index: {results.get('c_index', 'N/A'):.4f}")
        print(f"AIC: {results.get('aic', 'N/A')}")

    except Exception as e:
        print(f"✗ 模型评估失败: {e}")
        results = {}

    # 6. 特征重要性分析
    print("\n分析特征重要性...")
    try:
        importance = model.get_feature_importance()
        print("\n前10个最重要的特征:")
        print(importance.head(10)[['feature', 'coefficient', 'p_value']])

        # 可视化特征重要性
        plt.figure(figsize=(12, 8))
        top_features = importance.head(15)
        plt.barh(range(len(top_features)), top_features['abs_coefficient'])
        plt.yticks(range(len(top_features)), top_features['feature'])
        plt.xlabel('|Coefficient|')
        plt.title('Top 15 Feature Importance')
        plt.tight_layout()
        plt.savefig('plots/feature_importance_detailed.png', dpi=300, bbox_inches='tight')
        plt.show()

    except Exception as e:
        print(f"特征重要性分析失败: {e}")

    # 7. 预测示例
    print("\n进行预测示例...")
    try:
        # 选择几个测试样本进行预测
        sample_data = test_data.head(5)
        risk_scores = model.predict(sample_data)

        print("样本风险评分:")
        for i, (idx, score) in enumerate(zip(sample_data.index, risk_scores)):
            survival_time = sample_data.loc[idx, 'survival_time']
            event = sample_data.loc[idx, 'event']
            print(f"样本 {i+1}: 风险评分={score:.4f}, 生存时间={survival_time:.2f}, 事件={event}")

    except Exception as e:
        print(f"预测示例失败: {e}")

    return model, results

def compare_with_traditional_cox(df, stable_cox_model):
    """
    与传统 Cox 模型比较
    """
    print("\n" + "="*60)
    print("与传统 Cox 模型比较")
    print("="*60)

    try:
        from lifelines import CoxPHFitter

        # 处理数据
        processor = EquipmentDataProcessor()
        processed_data = processor.process_data(df)
        train_data, test_data = processor.split_data(processed_data, test_size=0.3)

        # 获取特征列
        feature_cols = Config.get_measurement_columns(processed_data)[:15]  # 使用前15个特征

        # 训练传统 Cox 模型
        traditional_cox = CoxPHFitter()
        cox_data = train_data[feature_cols + ['survival_time', 'event']]
        traditional_cox.fit(cox_data, duration_col='survival_time', event_col='event')

        # 比较性能
        stable_score = stable_cox_model.cox_model.score(test_data)
        traditional_score = traditional_cox.score(test_data[feature_cols + ['survival_time', 'event']])

        print(f"Stable Cox C-index: {stable_score:.4f}")
        print(f"Traditional Cox C-index: {traditional_score:.4f}")
        print(f"改进: {stable_score - traditional_score:.4f}")

        return {
            'stable_cox_score': stable_score,
            'traditional_cox_score': traditional_score,
            'improvement': stable_score - traditional_score
        }

    except Exception as e:
        print(f"比较分析失败: {e}")
        return {}

def main():
    """
    主函数：演示完整的 Stable Cox 分析流程
    按照 a.ipynb 逻辑和 StableCox 原理进行分析

    修改内容：
    1. 根据 a.ipynb 格式创建过程数据
    2. 应用事件过滤逻辑：event_0_mask | event_1_with_part_mask
    3. 保存数据到 ./Stable_Cox_Proj/data/ 目录
    4. 使用 StableCox 自动 S/V 特征选择
    """
    print("Stable Cox 装备材料测量数据分析")
    print("="*60)
    print("按照 a.ipynb 逻辑和 StableCox 原理进行分析")
    print("修改：使用自动特征选择，应用事件过滤逻辑")
    print("="*60)

    # 1. 数据管理器初始化和数据加载
    print("\n1. 初始化数据管理器并加载数据...")
    data_manager = EquipmentDataManager()
    df = data_manager.load_data() # OK

    if df is None:
        print("❌ 数据加载失败")
        return

    df['Date'] = pd.to_datetime(df['Date'], errors='coerce')

    print_data_summary(df, "原始数据")

    # 2. 数据预处理（按照 a.ipynb 逻辑，包含事件过滤）
    print("\n2. 数据预处理（包含事件过滤逻辑）...")
    df_merged1 = data_manager.preprocess_data(df)  # 返回的是过滤后的 df_merged1 OK

    # 3. 保存处理后的数据
    print("\n3. 保存处理后的数据...")
    try:
        saved_file = data_manager.save_processed_data() # OK
        print(f"✅ 数据已保存: {saved_file}")
    except Exception as e:
        print(f"❌ 数据保存失败: {e}")

    # 4. 运行 StableCox 分析（使用保存的数据和 SRDO + STG）
    print("\n4. 运行 StableCox 分析...")
    try:
        result = run_stable_cox_with_saved_data()
        if result is None:
            print("❌ StableCox 分析失败")
            return
        model, feature_selector, modeling_data = result
        print("✅ StableCox 分析完成")
    except Exception as e:
        print(f"❌ StableCox 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return

    # 5. 数据处理总结
    print("\n5. 数据处理总结...")
    print("   保存的数据框:")
    for key, value in data_manager.data_dict.items():
        if isinstance(value, pd.DataFrame):
            print(f"     {key}: {value.shape}")
        elif isinstance(value, list):
            print(f"     {key}: {len(value)} 项")

    # 6. 特征信息总结
    print("\n6. 特征信息总结...")
    excluded_cols = {'SN_Common', 'duration', 'event', 'Date', 'PO1 PO Number_ERT'}
    measurement_cols = [col for col in df_merged1.columns
                       if col not in excluded_cols and pd.api.types.is_numeric_dtype(df_merged1[col])]

    print(f"   总测量特征数: {len(measurement_cols)}")
    print(f"   样本数: {len(df_merged1)}")
    print(f"   事件率: {df_merged1['event'].mean():.3f}")
    print(f"   平均生存时间: {df_merged1['survival_time'].mean():.1f} 天")

    print(f"\n🎉 分析完成！")
    print(f"📁 数据已保存到 ./Stable_Cox_Proj/data/ 目录")
    print(f"📊 数据包含建模所需的所有列：SN_Common, all_measurement_cols, duration, event")
    print(f"🔧 已应用事件过滤逻辑：event_0_mask | event_1_with_part_mask")
    print(f"🤖 StableCox 模型使用自动 S/V 特征选择，无需手动特征选择")

    return model, feature_selector, data_manager

if __name__ == "__main__":
    result = main()
    if result:
        model, feature_selector, data_manager = result
        print(f"\n🎯 最终总结:")
        print(f"   模型类型: StableCox Equipment Model")
        print(f"   处理后样本: {len(data_manager.data_dict['df_merged1'])} 个设备")

        # 获取特征信息
        df_merged1 = data_manager.data_dict['df_merged1']
        excluded_cols = {'SN_Common', 'duration', 'event', 'Date', 'PO1 PO Number_ERT'}
        measurement_cols = [col for col in df_merged1.columns
                           if col not in excluded_cols and pd.api.types.is_numeric_dtype(df_merged1[col])]

        print(f"   原始测量特征数: {len(measurement_cols)} 个")

        # 获取选择的特征信息
        if feature_selector is not None:
            try:
                selected_features = feature_selector.get_selected_feature_names()
                weights = feature_selector.get_weights()
                print(f"   STG 选择特征数: {len(selected_features)} 个")
                print(f"   SRDO 权重统计: 均值={np.mean(weights):.4f}, 标准差={np.std(weights):.4f}")
            except Exception as e:
                print(f"   特征选择器信息获取失败: {e}")

        print(f"   事件率: {df_merged1['event'].mean():.1%}")
        print(f"   平均生存时间: {df_merged1['survival_time'].mean():.1f} 天")
        print(f"   数据已保存到 ./For_2025_05_26/Stable_Cox_Proj/data/ 目录")
        print(f"   模型已训练并可用于预测")
    else:
        print("❌ 程序执行失败")
