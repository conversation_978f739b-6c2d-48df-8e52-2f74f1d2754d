2025-05-27 11:22:26,453 [INFO] utils_equipment: ����Ŀ¼: models
2025-05-27 11:22:26,454 [INFO] utils_equipment: ����Ŀ¼: results
2025-05-27 11:22:26,454 [INFO] utils_equipment: ����Ŀ¼: plots
2025-05-27 11:22:26,454 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 11:22:26,455 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 11:22:26,458 [INFO] data_preprocessing: ��ʼ����װ�����ݣ�ԭʼ������״: (370, 37)
2025-05-27 11:22:26,458 [INFO] data_preprocessing: ��������ʱ����¼�ָʾ�������� a.ipynb �߼���
2025-05-27 11:22:26,459 [INFO] data_preprocessing: Time_Series ֵ�ֲ�: Time_Series
0    200
1    170
Name: count, dtype: int64
2025-05-27 11:22:26,460 [INFO] data_preprocessing: ������¼�� (Time_Series=0): 200
2025-05-27 11:22:26,460 [INFO] data_preprocessing: ά�޼�¼�� (Time_Series=1): 170
2025-05-27 11:22:26,460 [INFO] data_preprocessing: ��⵽ 7 ������У������Ƴ�
2025-05-27 11:22:26,460 [INFO] data_preprocessing: �����ʾ��: ['Part_12345_Status', 'Component_67890_Type', 'Module_11111_Version', 'Sensor_22222_Model', 'Cable_33333_Length']...
2025-05-27 11:22:26,461 [INFO] data_preprocessing: ���豸��: 200
2025-05-27 11:22:26,564 [INFO] data_preprocessing: �쳣���ͳ��:
2025-05-27 11:22:26,565 [INFO] data_preprocessing:   ά������������������: 0 ���豸
2025-05-27 11:22:26,565 [INFO] data_preprocessing:   ȱ����������: 0 ���豸
2025-05-27 11:22:26,565 [INFO] data_preprocessing:   ��Ч�豸��: 200
2025-05-27 11:22:26,567 [INFO] data_preprocessing: ��������������״: (200, 31)
2025-05-27 11:22:26,567 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:22:26,568 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:22:26,569 [INFO] data_preprocessing: ʹ�� COL.py �ж���������У��ҵ� 20 ��
2025-05-27 11:22:26,569 [INFO] data_preprocessing: ���ռ�⵽ 20 ����������
2025-05-27 11:22:26,569 [INFO] data_preprocessing: ������������
2025-05-27 11:22:26,587 [INFO] data_preprocessing: �����Ƴ�������
2025-05-27 11:22:26,588 [INFO] data_preprocessing: ��⵽������: NEAR_CONSTANT_COL, ����: 8.89157457679341e-25
2025-05-27 11:22:26,589 [INFO] data_preprocessing: �Ƴ��� 1 ��������
2025-05-27 11:22:26,589 [INFO] data_preprocessing: �Ƴ��ĳ�����: ['NEAR_CONSTANT_COL']
2025-05-27 11:22:26,590 [INFO] data_preprocessing: ����ȱʧֵ
2025-05-27 11:22:26,595 [INFO] data_preprocessing: ִ�������������
2025-05-27 11:22:26,597 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:22:26,597 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:22:26,599 [INFO] data_preprocessing: ���ݴ�����ɣ�����������״: (200, 30)
2025-05-27 11:22:26,602 [INFO] cox_model: ��ʼ����ѡ�񣬷���: cox_pvalue
2025-05-27 11:22:27,036 [INFO] cox_model: ����ѡ����ɣ�ѡ���� 0 ������
2025-05-27 11:22:27,036 [INFO] stable_cox_equipment: ����ѡ����ɣ��� 20 ��������ѡ���� 0 ��
2025-05-27 11:22:27,037 [INFO] cox_model: ��ʼ��ϼ�Ȩ Cox ģ��
2025-05-27 11:24:30,602 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 11:24:30,602 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 11:24:30,604 [INFO] data_preprocessing: ��ʼ����װ�����ݣ�ԭʼ������״: (370, 37)
2025-05-27 11:24:30,605 [INFO] data_preprocessing: ��������ʱ����¼�ָʾ�������� a.ipynb �߼���
2025-05-27 11:24:30,605 [INFO] data_preprocessing: Time_Series ֵ�ֲ�: Time_Series
0    200
1    170
Name: count, dtype: int64
2025-05-27 11:24:30,606 [INFO] data_preprocessing: ������¼�� (Time_Series=0): 200
2025-05-27 11:24:30,606 [INFO] data_preprocessing: ά�޼�¼�� (Time_Series=1): 170
2025-05-27 11:24:30,607 [INFO] data_preprocessing: ��⵽ 7 ������У������Ƴ�
2025-05-27 11:24:30,607 [INFO] data_preprocessing: �����ʾ��: ['Part_12345_Status', 'Component_67890_Type', 'Module_11111_Version', 'Sensor_22222_Model', 'Cable_33333_Length']...
2025-05-27 11:24:30,607 [INFO] data_preprocessing: ���豸��: 200
2025-05-27 11:24:30,708 [INFO] data_preprocessing: �쳣���ͳ��:
2025-05-27 11:24:30,709 [INFO] data_preprocessing:   ά������������������: 0 ���豸
2025-05-27 11:24:30,709 [INFO] data_preprocessing:   ȱ����������: 0 ���豸
2025-05-27 11:24:30,709 [INFO] data_preprocessing:   ��Ч�豸��: 200
2025-05-27 11:24:30,711 [INFO] data_preprocessing: ��������������״: (200, 31)
2025-05-27 11:24:30,711 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:24:30,711 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:24:30,712 [INFO] data_preprocessing: ʹ�� COL.py �ж���������У��ҵ� 20 ��
2025-05-27 11:24:30,712 [INFO] data_preprocessing: ���ռ�⵽ 20 ����������
2025-05-27 11:24:30,712 [INFO] data_preprocessing: ������������
2025-05-27 11:24:30,730 [INFO] data_preprocessing: �����Ƴ�������
2025-05-27 11:24:30,731 [INFO] data_preprocessing: ��⵽������: NEAR_CONSTANT_COL, ����: 8.89157457679341e-25
2025-05-27 11:24:30,732 [INFO] data_preprocessing: �Ƴ��� 1 ��������
2025-05-27 11:24:30,732 [INFO] data_preprocessing: �Ƴ��ĳ�����: ['NEAR_CONSTANT_COL']
2025-05-27 11:24:30,732 [INFO] data_preprocessing: ����ȱʧֵ
2025-05-27 11:24:30,737 [INFO] data_preprocessing: ִ�������������
2025-05-27 11:24:30,738 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:24:30,738 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:24:30,741 [INFO] data_preprocessing: ���ݴ�����ɣ�����������״: (200, 30)
2025-05-27 11:24:30,743 [INFO] cox_model: ��ʼ����ѡ�񣬷���: cox_pvalue
2025-05-27 11:24:30,743 [INFO] cox_model: ��ʼ���� 20 ����ѡ����
2025-05-27 11:24:31,252 [INFO] cox_model: �ɹ������� 20 ������
2025-05-27 11:24:31,252 [WARNING] cox_model: ʹ��ԭʼ��ֵ 0.05 ֻѡ���� 0 �����������ÿ��ɲ���
2025-05-27 11:24:31,252 [INFO] cox_model: ����ѡ���� 5 ������
2025-05-27 11:24:31,252 [INFO] cox_model: ѡ�������: ['Z_CLOSED_LOOP_V700_H_Y', '0A_Flattop', 'Z_CLOSED_LOOP_V700_L_X', 'Z_CLOSED_LOOP_V700_H_Z', 'Z_CLOSED_LOOP_V700_L_Y']
2025-05-27 11:24:31,252 [INFO] cox_model: ����ѡ����ɣ�ѡ���� 5 ������
2025-05-27 11:24:31,253 [INFO] stable_cox_equipment: ����ѡ����ɣ��� 20 ��������ѡ���� 5 ��
2025-05-27 11:24:31,253 [INFO] cox_model: ��ʼ��ϼ�Ȩ Cox ģ��
2025-05-27 11:24:31,253 [INFO] cox_model: ʹ�� 5 ���������н�ģ
2025-05-27 11:24:51,553 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 11:24:51,554 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 11:24:51,556 [INFO] data_preprocessing: ��ʼ����װ�����ݣ�ԭʼ������״: (370, 37)
2025-05-27 11:24:51,557 [INFO] data_preprocessing: ��������ʱ����¼�ָʾ�������� a.ipynb �߼���
2025-05-27 11:24:51,557 [INFO] data_preprocessing: Time_Series ֵ�ֲ�: Time_Series
0    200
1    170
Name: count, dtype: int64
2025-05-27 11:24:51,558 [INFO] data_preprocessing: ������¼�� (Time_Series=0): 200
2025-05-27 11:24:51,558 [INFO] data_preprocessing: ά�޼�¼�� (Time_Series=1): 170
2025-05-27 11:24:51,558 [INFO] data_preprocessing: ��⵽ 7 ������У������Ƴ�
2025-05-27 11:24:51,559 [INFO] data_preprocessing: �����ʾ��: ['Part_12345_Status', 'Component_67890_Type', 'Module_11111_Version', 'Sensor_22222_Model', 'Cable_33333_Length']...
2025-05-27 11:24:51,559 [INFO] data_preprocessing: ���豸��: 200
2025-05-27 11:24:51,649 [INFO] data_preprocessing: �쳣���ͳ��:
2025-05-27 11:24:51,649 [INFO] data_preprocessing:   ά������������������: 0 ���豸
2025-05-27 11:24:51,649 [INFO] data_preprocessing:   ȱ����������: 0 ���豸
2025-05-27 11:24:51,649 [INFO] data_preprocessing:   ��Ч�豸��: 200
2025-05-27 11:24:51,651 [INFO] data_preprocessing: ��������������״: (200, 31)
2025-05-27 11:24:51,651 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:24:51,652 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:24:51,652 [INFO] data_preprocessing: ʹ�� COL.py �ж���������У��ҵ� 20 ��
2025-05-27 11:24:51,653 [INFO] data_preprocessing: ���ռ�⵽ 20 ����������
2025-05-27 11:24:51,653 [INFO] data_preprocessing: ������������
2025-05-27 11:24:51,669 [INFO] data_preprocessing: �����Ƴ�������
2025-05-27 11:24:51,670 [INFO] data_preprocessing: ��⵽������: NEAR_CONSTANT_COL, ����: 8.89157457679341e-25
2025-05-27 11:24:51,671 [INFO] data_preprocessing: �Ƴ��� 1 ��������
2025-05-27 11:24:51,671 [INFO] data_preprocessing: �Ƴ��ĳ�����: ['NEAR_CONSTANT_COL']
2025-05-27 11:24:51,671 [INFO] data_preprocessing: ����ȱʧֵ
2025-05-27 11:24:51,675 [INFO] data_preprocessing: ִ�������������
2025-05-27 11:24:51,676 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:24:51,676 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:24:51,678 [INFO] data_preprocessing: ���ݴ�����ɣ�����������״: (200, 30)
2025-05-27 11:24:51,680 [INFO] cox_model: ��ʼ����ѡ�񣬷���: cox_pvalue
2025-05-27 11:24:51,681 [INFO] cox_model: ��ʼ���� 20 ����ѡ����
2025-05-27 11:24:52,149 [INFO] cox_model: �ɹ������� 20 ������
2025-05-27 11:24:52,149 [WARNING] cox_model: ʹ��ԭʼ��ֵ 0.05 ֻѡ���� 0 �����������ÿ��ɲ���
2025-05-27 11:24:52,149 [INFO] cox_model: ����ѡ���� 5 ������
2025-05-27 11:24:52,149 [INFO] cox_model: ѡ�������: ['Z_CLOSED_LOOP_V700_H_Y', '0A_Flattop', 'Z_CLOSED_LOOP_V700_L_X', 'Z_CLOSED_LOOP_V700_H_Z', 'Z_CLOSED_LOOP_V700_L_Y']
2025-05-27 11:24:52,150 [INFO] cox_model: ����ѡ����ɣ�ѡ���� 5 ������
2025-05-27 11:24:52,150 [INFO] stable_cox_equipment: ����ѡ����ɣ��� 20 ��������ѡ���� 5 ��
2025-05-27 11:24:52,150 [INFO] cox_model: ��ʼ��ϼ�Ȩ Cox ģ��
2025-05-27 11:24:52,151 [INFO] cox_model: ʹ�� 5 ���������н�ģ
2025-05-27 11:24:52,177 [INFO] cox_model: Cox ģ�������ɣ�������: 5
2025-05-27 11:24:52,178 [INFO] cox_model: ģ�� C-index: 0.5699
2025-05-27 11:24:52,180 [INFO] utils_equipment: ģ���ѱ��浽: models\stable_cox_equipment_model.pkl
2025-05-27 11:24:52,181 [INFO] stable_cox_equipment: Stable Cox ģ��ѵ�����
2025-05-27 11:25:13,374 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 11:25:13,375 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 11:25:13,377 [INFO] data_preprocessing: ��ʼ����װ�����ݣ�ԭʼ������״: (370, 37)
2025-05-27 11:25:13,377 [INFO] data_preprocessing: ��������ʱ����¼�ָʾ�������� a.ipynb �߼���
2025-05-27 11:25:13,378 [INFO] data_preprocessing: Time_Series ֵ�ֲ�: Time_Series
0    200
1    170
Name: count, dtype: int64
2025-05-27 11:25:13,378 [INFO] data_preprocessing: ������¼�� (Time_Series=0): 200
2025-05-27 11:25:13,379 [INFO] data_preprocessing: ά�޼�¼�� (Time_Series=1): 170
2025-05-27 11:25:13,379 [INFO] data_preprocessing: ��⵽ 7 ������У������Ƴ�
2025-05-27 11:25:13,379 [INFO] data_preprocessing: �����ʾ��: ['Part_12345_Status', 'Component_67890_Type', 'Module_11111_Version', 'Sensor_22222_Model', 'Cable_33333_Length']...
2025-05-27 11:25:13,379 [INFO] data_preprocessing: ���豸��: 200
2025-05-27 11:25:13,481 [INFO] data_preprocessing: �쳣���ͳ��:
2025-05-27 11:25:13,481 [INFO] data_preprocessing:   ά������������������: 0 ���豸
2025-05-27 11:25:13,482 [INFO] data_preprocessing:   ȱ����������: 0 ���豸
2025-05-27 11:25:13,482 [INFO] data_preprocessing:   ��Ч�豸��: 200
2025-05-27 11:25:13,484 [INFO] data_preprocessing: ��������������״: (200, 31)
2025-05-27 11:25:13,484 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:25:13,484 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:25:13,485 [INFO] data_preprocessing: ʹ�� COL.py �ж���������У��ҵ� 20 ��
2025-05-27 11:25:13,485 [INFO] data_preprocessing: ���ռ�⵽ 20 ����������
2025-05-27 11:25:13,485 [INFO] data_preprocessing: ������������
2025-05-27 11:25:13,501 [INFO] data_preprocessing: �����Ƴ�������
2025-05-27 11:25:13,502 [INFO] data_preprocessing: ��⵽������: NEAR_CONSTANT_COL, ����: 8.89157457679341e-25
2025-05-27 11:25:13,503 [INFO] data_preprocessing: �Ƴ��� 1 ��������
2025-05-27 11:25:13,503 [INFO] data_preprocessing: �Ƴ��ĳ�����: ['NEAR_CONSTANT_COL']
2025-05-27 11:25:13,504 [INFO] data_preprocessing: ����ȱʧֵ
2025-05-27 11:25:13,508 [INFO] data_preprocessing: ִ�������������
2025-05-27 11:25:13,509 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:25:13,509 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:25:13,512 [INFO] data_preprocessing: ���ݴ�����ɣ�����������״: (200, 30)
2025-05-27 11:25:13,514 [INFO] cox_model: ��ʼ����ѡ�񣬷���: cox_pvalue
2025-05-27 11:25:13,515 [INFO] cox_model: ��ʼ���� 20 ����ѡ����
2025-05-27 11:25:14,026 [INFO] cox_model: �ɹ������� 20 ������
2025-05-27 11:25:14,026 [WARNING] cox_model: ʹ��ԭʼ��ֵ 0.05 ֻѡ���� 0 �����������ÿ��ɲ���
2025-05-27 11:25:14,026 [INFO] cox_model: ����ѡ���� 5 ������
2025-05-27 11:25:14,026 [INFO] cox_model: ѡ�������: ['Z_CLOSED_LOOP_V700_H_Y', '0A_Flattop', 'Z_CLOSED_LOOP_V700_L_X', 'Z_CLOSED_LOOP_V700_H_Z', 'Z_CLOSED_LOOP_V700_L_Y']
2025-05-27 11:25:14,027 [INFO] cox_model: ����ѡ����ɣ�ѡ���� 5 ������
2025-05-27 11:25:14,027 [INFO] stable_cox_equipment: ����ѡ����ɣ��� 20 ��������ѡ���� 5 ��
2025-05-27 11:25:14,027 [INFO] cox_model: ��ʼ��ϼ�Ȩ Cox ģ��
2025-05-27 11:25:14,027 [INFO] cox_model: ʹ�� 5 ���������н�ģ
2025-05-27 11:25:14,052 [INFO] cox_model: Cox ģ�������ɣ�������: 5
2025-05-27 11:25:14,054 [INFO] cox_model: ģ�� C-index: 0.5699
2025-05-27 11:25:14,055 [INFO] utils_equipment: ģ���ѱ��浽: models\stable_cox_equipment_model.pkl
2025-05-27 11:25:14,056 [INFO] stable_cox_equipment: Stable Cox ģ��ѵ�����
2025-05-27 11:25:14,056 [INFO] data_preprocessing: ��ʼ����װ�����ݣ�ԭʼ������״: (370, 37)
2025-05-27 11:25:14,056 [INFO] data_preprocessing: ��������ʱ����¼�ָʾ�������� a.ipynb �߼���
2025-05-27 11:25:14,057 [INFO] data_preprocessing: Time_Series ֵ�ֲ�: Time_Series
0    200
1    170
Name: count, dtype: int64
2025-05-27 11:25:14,058 [INFO] data_preprocessing: ������¼�� (Time_Series=0): 200
2025-05-27 11:25:14,058 [INFO] data_preprocessing: ά�޼�¼�� (Time_Series=1): 170
2025-05-27 11:25:14,058 [INFO] data_preprocessing: ��⵽ 7 ������У������Ƴ�
2025-05-27 11:25:14,058 [INFO] data_preprocessing: �����ʾ��: ['Part_12345_Status', 'Component_67890_Type', 'Module_11111_Version', 'Sensor_22222_Model', 'Cable_33333_Length']...
2025-05-27 11:25:14,059 [INFO] data_preprocessing: ���豸��: 200
2025-05-27 11:25:14,161 [INFO] data_preprocessing: �쳣���ͳ��:
2025-05-27 11:25:14,162 [INFO] data_preprocessing:   ά������������������: 0 ���豸
2025-05-27 11:25:14,162 [INFO] data_preprocessing:   ȱ����������: 0 ���豸
2025-05-27 11:25:14,162 [INFO] data_preprocessing:   ��Ч�豸��: 200
2025-05-27 11:25:14,164 [INFO] data_preprocessing: ��������������״: (200, 31)
2025-05-27 11:25:14,164 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:25:14,164 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:25:14,165 [INFO] data_preprocessing: ʹ�� COL.py �ж���������У��ҵ� 20 ��
2025-05-27 11:25:14,165 [INFO] data_preprocessing: ���ռ�⵽ 20 ����������
2025-05-27 11:25:14,165 [INFO] data_preprocessing: ������������
2025-05-27 11:25:14,181 [INFO] data_preprocessing: �����Ƴ�������
2025-05-27 11:25:14,182 [INFO] data_preprocessing: ��⵽������: NEAR_CONSTANT_COL, ����: 8.89157457679341e-25
2025-05-27 11:25:14,183 [INFO] data_preprocessing: �Ƴ��� 1 ��������
2025-05-27 11:25:14,183 [INFO] data_preprocessing: �Ƴ��ĳ�����: ['NEAR_CONSTANT_COL']
2025-05-27 11:25:14,184 [INFO] data_preprocessing: ����ȱʧֵ
2025-05-27 11:25:14,188 [INFO] data_preprocessing: ִ�������������
2025-05-27 11:25:14,189 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:25:14,189 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:25:14,191 [INFO] data_preprocessing: ���ݴ�����ɣ�����������״: (200, 30)
2025-05-27 11:25:36,031 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 11:25:36,031 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 11:25:36,034 [INFO] data_preprocessing: ��ʼ����װ�����ݣ�ԭʼ������״: (370, 37)
2025-05-27 11:25:36,034 [INFO] data_preprocessing: ��������ʱ����¼�ָʾ�������� a.ipynb �߼���
2025-05-27 11:25:36,034 [INFO] data_preprocessing: Time_Series ֵ�ֲ�: Time_Series
0    200
1    170
Name: count, dtype: int64
2025-05-27 11:25:36,035 [INFO] data_preprocessing: ������¼�� (Time_Series=0): 200
2025-05-27 11:25:36,036 [INFO] data_preprocessing: ά�޼�¼�� (Time_Series=1): 170
2025-05-27 11:25:36,036 [INFO] data_preprocessing: ��⵽ 7 ������У������Ƴ�
2025-05-27 11:25:36,036 [INFO] data_preprocessing: �����ʾ��: ['Part_12345_Status', 'Component_67890_Type', 'Module_11111_Version', 'Sensor_22222_Model', 'Cable_33333_Length']...
2025-05-27 11:25:36,037 [INFO] data_preprocessing: ���豸��: 200
2025-05-27 11:25:36,130 [INFO] data_preprocessing: �쳣���ͳ��:
2025-05-27 11:25:36,130 [INFO] data_preprocessing:   ά������������������: 0 ���豸
2025-05-27 11:25:36,130 [INFO] data_preprocessing:   ȱ����������: 0 ���豸
2025-05-27 11:25:36,131 [INFO] data_preprocessing:   ��Ч�豸��: 200
2025-05-27 11:25:36,133 [INFO] data_preprocessing: ��������������״: (200, 31)
2025-05-27 11:25:36,133 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:25:36,133 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:25:36,133 [INFO] data_preprocessing: ʹ�� COL.py �ж���������У��ҵ� 20 ��
2025-05-27 11:25:36,134 [INFO] data_preprocessing: ���ռ�⵽ 20 ����������
2025-05-27 11:25:36,134 [INFO] data_preprocessing: ������������
2025-05-27 11:25:36,151 [INFO] data_preprocessing: �����Ƴ�������
2025-05-27 11:25:36,152 [INFO] data_preprocessing: ��⵽������: NEAR_CONSTANT_COL, ����: 8.89157457679341e-25
2025-05-27 11:25:36,153 [INFO] data_preprocessing: �Ƴ��� 1 ��������
2025-05-27 11:25:36,153 [INFO] data_preprocessing: �Ƴ��ĳ�����: ['NEAR_CONSTANT_COL']
2025-05-27 11:25:36,153 [INFO] data_preprocessing: ����ȱʧֵ
2025-05-27 11:25:36,158 [INFO] data_preprocessing: ִ�������������
2025-05-27 11:25:36,158 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:25:36,159 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:25:36,161 [INFO] data_preprocessing: ���ݴ�����ɣ�����������״: (200, 30)
2025-05-27 11:25:36,164 [INFO] cox_model: ��ʼ����ѡ�񣬷���: cox_pvalue
2025-05-27 11:25:36,164 [INFO] cox_model: ��ʼ���� 20 ����ѡ����
2025-05-27 11:25:36,678 [INFO] cox_model: �ɹ������� 20 ������
2025-05-27 11:25:36,678 [WARNING] cox_model: ʹ��ԭʼ��ֵ 0.05 ֻѡ���� 0 �����������ÿ��ɲ���
2025-05-27 11:25:36,678 [INFO] cox_model: ����ѡ���� 5 ������
2025-05-27 11:25:36,678 [INFO] cox_model: ѡ�������: ['Z_CLOSED_LOOP_V700_H_Y', '0A_Flattop', 'Z_CLOSED_LOOP_V700_L_X', 'Z_CLOSED_LOOP_V700_H_Z', 'Z_CLOSED_LOOP_V700_L_Y']
2025-05-27 11:25:36,678 [INFO] cox_model: ����ѡ����ɣ�ѡ���� 5 ������
2025-05-27 11:25:36,679 [INFO] stable_cox_equipment: ����ѡ����ɣ��� 20 ��������ѡ���� 5 ��
2025-05-27 11:25:36,679 [INFO] cox_model: ��ʼ��ϼ�Ȩ Cox ģ��
2025-05-27 11:25:36,679 [INFO] cox_model: ʹ�� 5 ���������н�ģ
2025-05-27 11:25:36,706 [INFO] cox_model: Cox ģ�������ɣ�������: 5
2025-05-27 11:25:36,707 [INFO] cox_model: ģ�� C-index: 0.5699
2025-05-27 11:25:36,708 [INFO] utils_equipment: ģ���ѱ��浽: models\stable_cox_equipment_model.pkl
2025-05-27 11:25:36,709 [INFO] stable_cox_equipment: Stable Cox ģ��ѵ�����
2025-05-27 11:25:36,709 [INFO] data_preprocessing: ��ʼ����װ�����ݣ�ԭʼ������״: (370, 37)
2025-05-27 11:25:36,709 [INFO] data_preprocessing: ��������ʱ����¼�ָʾ�������� a.ipynb �߼���
2025-05-27 11:25:36,709 [INFO] data_preprocessing: Time_Series ֵ�ֲ�: Time_Series
0    200
1    170
Name: count, dtype: int64
2025-05-27 11:25:36,710 [INFO] data_preprocessing: ������¼�� (Time_Series=0): 200
2025-05-27 11:25:36,711 [INFO] data_preprocessing: ά�޼�¼�� (Time_Series=1): 170
2025-05-27 11:25:36,711 [INFO] data_preprocessing: ��⵽ 7 ������У������Ƴ�
2025-05-27 11:25:36,711 [INFO] data_preprocessing: �����ʾ��: ['Part_12345_Status', 'Component_67890_Type', 'Module_11111_Version', 'Sensor_22222_Model', 'Cable_33333_Length']...
2025-05-27 11:25:36,711 [INFO] data_preprocessing: ���豸��: 200
2025-05-27 11:25:36,815 [INFO] data_preprocessing: �쳣���ͳ��:
2025-05-27 11:25:36,815 [INFO] data_preprocessing:   ά������������������: 0 ���豸
2025-05-27 11:25:36,816 [INFO] data_preprocessing:   ȱ����������: 0 ���豸
2025-05-27 11:25:36,816 [INFO] data_preprocessing:   ��Ч�豸��: 200
2025-05-27 11:25:36,818 [INFO] data_preprocessing: ��������������״: (200, 31)
2025-05-27 11:25:36,818 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:25:36,818 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:25:36,819 [INFO] data_preprocessing: ʹ�� COL.py �ж���������У��ҵ� 20 ��
2025-05-27 11:25:36,819 [INFO] data_preprocessing: ���ռ�⵽ 20 ����������
2025-05-27 11:25:36,819 [INFO] data_preprocessing: ������������
2025-05-27 11:25:36,836 [INFO] data_preprocessing: �����Ƴ�������
2025-05-27 11:25:36,837 [INFO] data_preprocessing: ��⵽������: NEAR_CONSTANT_COL, ����: 8.89157457679341e-25
2025-05-27 11:25:36,838 [INFO] data_preprocessing: �Ƴ��� 1 ��������
2025-05-27 11:25:36,838 [INFO] data_preprocessing: �Ƴ��ĳ�����: ['NEAR_CONSTANT_COL']
2025-05-27 11:25:36,838 [INFO] data_preprocessing: ����ȱʧֵ
2025-05-27 11:25:36,842 [INFO] data_preprocessing: ִ�������������
2025-05-27 11:25:36,843 [INFO] data_preprocessing: ����ʱ��ͳ�� - ��ֵ: 215.00, ��λ��: 230.00
2025-05-27 11:25:36,843 [INFO] data_preprocessing: �¼���: 0.850
2025-05-27 11:25:36,845 [INFO] data_preprocessing: ���ݴ�����ɣ�����������״: (200, 30)
2025-05-27 13:39:42,460 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 13:39:42,461 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 13:39:42,468 [WARNING] stable_cox_equipment: ���ݸ�ʽ����: ["��ȱʧ���� (>80%): ['PO1 PO Number_ERT', 'Item Description_ERT', 'Vendor Site Code_ERT', 'Vendor Country Code_ERT', 'Vendor Pole_ERT', 'Failure Code_ERT']"]
2025-05-27 13:39:42,468 [INFO] stable_cox_equipment: ʹ�� 181 ���������н�ģ
2025-05-27 13:39:42,470 [INFO] srdo_algorithm: ʹ�� SRDO �ؼ�Ȩ���ȶ�������: 90
2025-05-27 13:39:42,470 [INFO] srdo_algorithm: ��ʼ SRDO �㷨��������: 2927, ������: 181
2025-05-27 13:39:42,491 [INFO] srdo_algorithm: ԭʼ�����������: 6949.8694
2025-05-27 13:39:43,824 [INFO] srdo_algorithm: SRDO ��ɣ�Ȩ��ͳ�� - ��ֵ: 1.0000, ��׼��: 0.5218, ���ֵ: 2.2305
2025-05-27 13:39:43,825 [INFO] stable_cox_equipment: SRDO �ؼ�Ȩ��ɣ�Ȩ��ͳ�� - ��ֵ: 1.0000, ��׼��: 0.5218
2025-05-27 13:39:43,828 [INFO] cox_model: ��ʼ��ϼ�Ȩ Cox ģ��
2025-05-27 13:39:43,828 [INFO] cox_model: ʹ�� 181 ���������н�ģ
2025-05-27 13:39:45,222 [INFO] cox_model: Cox ģ�������ɣ�������: 181
2025-05-27 13:39:45,236 [INFO] cox_model: ģ�� C-index: 0.5749
2025-05-27 13:39:45,239 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 13:39:45,241 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 13:39:45,243 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 13:39:45,245 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 13:39:45,246 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 13:39:45,246 [INFO] stable_cox_equipment: ������֤ C-index: 0.0000
2025-05-27 13:39:45,249 [INFO] utils_equipment: ģ���ѱ��浽: models\stable_cox_equipment_model.pkl
2025-05-27 13:39:45,249 [INFO] stable_cox_equipment: Stable Cox ģ��ѵ�����
2025-05-27 13:39:45,254 [INFO] stable_cox_equipment: ��ʼģ������
2025-05-27 13:39:45,255 [INFO] evaluation: ��ʼģ������
2025-05-27 13:39:45,265 [WARNING] evaluation: C-index ����ʧ��: 'weights'
2025-05-27 13:39:45,267 [WARNING] evaluation: ������Ȼ����ʧ��: 'weights'
2025-05-27 13:39:45,267 [WARNING] evaluation: AIC ����ʧ��: Since the model is semi-parametric (and not fully-parametric), the AIC does not exist. You probably want the `.AIC_partial_` property instead.
2025-05-27 13:39:45,267 [INFO] evaluation: ִ�� 3 ����շ���
2025-05-27 13:39:45,305 [INFO] evaluation: ���ɿ��ӻ�ͼ��
2025-05-27 13:40:09,410 [INFO] evaluation: ģ��������� - C-index: nan, AIC: nan
2025-05-27 13:40:09,411 [INFO] evaluation: ���������ѱ��浽: plots\evaluation_report.md
2025-05-27 13:40:56,239 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 13:40:56,240 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 13:40:56,249 [WARNING] stable_cox_equipment: ���ݸ�ʽ����: ["��ȱʧ���� (>80%): ['PO1 PO Number_ERT', 'Item Description_ERT', 'Vendor Site Code_ERT', 'Vendor Country Code_ERT', 'Vendor Pole_ERT', 'Failure Code_ERT']"]
2025-05-27 13:40:56,250 [INFO] stable_cox_equipment: ʹ�� 181 ���������н�ģ
2025-05-27 13:40:56,253 [INFO] srdo_algorithm: ʹ�� SRDO �ؼ�Ȩ���ȶ�������: 90
2025-05-27 13:40:56,254 [INFO] srdo_algorithm: ��ʼ SRDO �㷨��������: 2927, ������: 181
2025-05-27 13:40:56,278 [INFO] srdo_algorithm: ԭʼ�����������: 6949.8694
2025-05-27 13:40:59,195 [INFO] srdo_algorithm: SRDO ��ɣ�Ȩ��ͳ�� - ��ֵ: 1.0000, ��׼��: 0.5218, ���ֵ: 2.2305
2025-05-27 13:40:59,199 [INFO] stable_cox_equipment: SRDO �ؼ�Ȩ��ɣ�Ȩ��ͳ�� - ��ֵ: 1.0000, ��׼��: 0.5218
2025-05-27 13:40:59,205 [INFO] cox_model: ��ʼ��ϼ�Ȩ Cox ģ��
2025-05-27 13:40:59,206 [INFO] cox_model: ʹ�� 181 ���������н�ģ
2025-05-27 13:41:01,659 [INFO] cox_model: Cox ģ�������ɣ�������: 181
2025-05-27 13:41:01,671 [INFO] cox_model: ģ�� C-index: 0.5749
2025-05-27 13:41:01,674 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 13:41:01,676 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 13:41:01,677 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 13:41:01,678 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 13:41:01,680 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 13:41:01,680 [INFO] stable_cox_equipment: ������֤ C-index: 0.0000
2025-05-27 13:41:01,682 [INFO] utils_equipment: ģ���ѱ��浽: models\stable_cox_equipment_model.pkl
2025-05-27 13:41:01,683 [INFO] stable_cox_equipment: Stable Cox ģ��ѵ�����
2025-05-27 13:41:01,687 [INFO] stable_cox_equipment: ��ʼģ������
2025-05-27 13:41:01,687 [INFO] evaluation: ��ʼģ������
2025-05-27 13:41:01,697 [WARNING] evaluation: C-index ����ʧ��: 'weights'
2025-05-27 13:41:01,699 [WARNING] evaluation: ������Ȼ����ʧ��: 'weights'
2025-05-27 13:41:01,700 [WARNING] evaluation: AIC ����ʧ��: Since the model is semi-parametric (and not fully-parametric), the AIC does not exist. You probably want the `.AIC_partial_` property instead.
2025-05-27 13:41:01,700 [INFO] evaluation: ִ�� 3 ����շ���
2025-05-27 13:41:01,733 [INFO] evaluation: ���ɿ��ӻ�ͼ��
2025-05-27 13:41:16,830 [INFO] evaluation: ģ��������� - C-index: nan, AIC: nan
2025-05-27 13:41:16,831 [INFO] evaluation: ���������ѱ��浽: plots\evaluation_report.md
2025-05-27 14:26:48,921 [INFO] utils_equipment: ����Ŀ¼: models
2025-05-27 14:26:48,923 [INFO] utils_equipment: ����Ŀ¼: results
2025-05-27 14:26:48,924 [INFO] utils_equipment: ����Ŀ¼: plots
2025-05-27 14:26:48,924 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 14:26:48,926 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 14:26:48,933 [WARNING] stable_cox_equipment: ���ݸ�ʽ����: ["��ȱʧ���� (>80%): ['PO1 PO Number_ERT', 'Item Description_ERT', 'Vendor Site Code_ERT', 'Vendor Country Code_ERT', 'Vendor Pole_ERT', 'Failure Code_ERT']"]
2025-05-27 14:26:48,933 [INFO] stable_cox_equipment: ʹ�� 162 ���������н�ģ
2025-05-27 14:26:48,936 [INFO] cox_model: ��ʼ����ѡ�񣬷���: auto
2025-05-27 14:27:29,681 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 14:27:29,683 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 14:27:29,688 [WARNING] stable_cox_equipment: ���ݸ�ʽ����: ["��ȱʧ���� (>80%): ['PO1 PO Number_ERT', 'Item Description_ERT', 'Vendor Site Code_ERT', 'Vendor Country Code_ERT', 'Vendor Pole_ERT', 'Failure Code_ERT']"]
2025-05-27 14:27:29,689 [INFO] stable_cox_equipment: ʹ�� 162 ���������н�ģ
2025-05-27 14:27:29,691 [INFO] cox_model: ��ʼ����ѡ�񣬷���: cox_pvalue
2025-05-27 14:27:29,691 [INFO] cox_model: ��ʼ���� 162 ����ѡ����
2025-05-27 14:27:46,677 [INFO] cox_model: �ɹ������� 162 ������
2025-05-27 14:27:46,677 [INFO] cox_model: ����ѡ���� 6 ������
2025-05-27 14:27:46,677 [INFO] cox_model: ѡ�������: ['PULSE_280V_Z1_LOW', '300V_V700_L_Z', '300V_V280_H_X', 'PULSE_280V_Z2_LOW', '300V_V280_L_X']...
2025-05-27 14:27:46,677 [INFO] cox_model: ����ѡ����ɣ�ѡ���� 6 ������
2025-05-27 14:27:46,678 [INFO] stable_cox_equipment: ����ѡ����ɣ��� 162 ��������ѡ���� 6 ��
2025-05-27 14:27:46,678 [INFO] srdo_algorithm: ʹ�� SRDO �ؼ�Ȩ���ȶ�������: 3
2025-05-27 14:27:46,678 [INFO] srdo_algorithm: ��ʼ SRDO �㷨��������: 2927, ������: 6
2025-05-27 14:27:46,679 [INFO] srdo_algorithm: ԭʼ�����������: 14.1692
2025-05-27 14:27:46,943 [INFO] srdo_algorithm: SRDO ��ɣ�Ȩ��ͳ�� - ��ֵ: 1.0000, ��׼��: 0.0890, ���ֵ: 1.1621
2025-05-27 14:27:46,943 [INFO] stable_cox_equipment: SRDO �ؼ�Ȩ��ɣ�Ȩ��ͳ�� - ��ֵ: 1.0000, ��׼��: 0.0890
2025-05-27 14:27:46,944 [INFO] cox_model: ��ʼ��ϼ�Ȩ Cox ģ��
2025-05-27 14:27:46,944 [INFO] cox_model: ʹ�� 6 ���������н�ģ
2025-05-27 14:27:47,056 [INFO] cox_model: Cox ģ�������ɣ�������: 6
2025-05-27 14:27:47,068 [INFO] cox_model: ģ�� C-index: 0.5189
2025-05-27 14:27:47,069 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:27:47,069 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:27:47,070 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:27:47,070 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:27:47,070 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:27:47,070 [INFO] stable_cox_equipment: ������֤ C-index: 0.0000
2025-05-27 14:27:47,072 [INFO] utils_equipment: ģ���ѱ��浽: models\stable_cox_equipment_model.pkl
2025-05-27 14:27:47,072 [INFO] stable_cox_equipment: Stable Cox ģ��ѵ�����
2025-05-27 14:27:47,076 [INFO] stable_cox_equipment: ��ʼģ������
2025-05-27 14:27:47,076 [INFO] evaluation: ��ʼģ������
2025-05-27 14:27:47,079 [WARNING] evaluation: C-index ����ʧ��: 'weights'
2025-05-27 14:27:47,080 [WARNING] evaluation: ������Ȼ����ʧ��: 'weights'
2025-05-27 14:27:47,080 [WARNING] evaluation: AIC ����ʧ��: Since the model is semi-parametric (and not fully-parametric), the AIC does not exist. You probably want the `.AIC_partial_` property instead.
2025-05-27 14:27:47,081 [INFO] evaluation: ִ�� 3 ����շ���
2025-05-27 14:27:47,111 [INFO] evaluation: ���ɿ��ӻ�ͼ��
2025-05-27 14:27:59,169 [INFO] evaluation: ģ��������� - C-index: nan, AIC: nan
2025-05-27 14:27:59,170 [INFO] evaluation: ���������ѱ��浽: plots\evaluation_report.md
2025-05-27 14:28:37,216 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 14:28:37,217 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 14:28:37,223 [WARNING] stable_cox_equipment: ���ݸ�ʽ����: ["��ȱʧ���� (>80%): ['PO1 PO Number_ERT', 'Item Description_ERT', 'Vendor Site Code_ERT', 'Vendor Country Code_ERT', 'Vendor Pole_ERT', 'Failure Code_ERT']"]
2025-05-27 14:28:37,223 [INFO] stable_cox_equipment: ʹ�� 162 ���������н�ģ
2025-05-27 14:28:37,225 [INFO] cox_model: ��ʼ����ѡ�񣬷���: cox_pvalue
2025-05-27 14:28:37,226 [INFO] cox_model: ��ʼ���� 162 ����ѡ����
2025-05-27 14:28:55,202 [INFO] cox_model: �ɹ������� 162 ������
2025-05-27 14:28:55,202 [INFO] cox_model: ����ѡ���� 6 ������
2025-05-27 14:28:55,202 [INFO] cox_model: ѡ�������: ['PULSE_280V_Z1_LOW', '300V_V700_L_Z', '300V_V280_H_X', 'PULSE_280V_Z2_LOW', '300V_V280_L_X']...
2025-05-27 14:28:55,202 [INFO] cox_model: ����ѡ����ɣ�ѡ���� 6 ������
2025-05-27 14:28:55,202 [INFO] stable_cox_equipment: ����ѡ����ɣ��� 162 ��������ѡ���� 6 ��
2025-05-27 14:28:55,203 [INFO] srdo_algorithm: ʹ�� SRDO �ؼ�Ȩ���ȶ�������: 3
2025-05-27 14:28:55,203 [INFO] srdo_algorithm: ��ʼ SRDO �㷨��������: 2927, ������: 6
2025-05-27 14:28:55,204 [INFO] srdo_algorithm: ԭʼ�����������: 14.1692
2025-05-27 14:28:55,486 [INFO] srdo_algorithm: SRDO ��ɣ�Ȩ��ͳ�� - ��ֵ: 1.0000, ��׼��: 0.0890, ���ֵ: 1.1621
2025-05-27 14:28:55,486 [INFO] stable_cox_equipment: SRDO �ؼ�Ȩ��ɣ�Ȩ��ͳ�� - ��ֵ: 1.0000, ��׼��: 0.0890
2025-05-27 14:28:55,487 [INFO] cox_model: ��ʼ��ϼ�Ȩ Cox ģ��
2025-05-27 14:28:55,487 [INFO] cox_model: ʹ�� 6 ���������н�ģ
2025-05-27 14:28:55,603 [INFO] cox_model: Cox ģ�������ɣ�������: 6
2025-05-27 14:28:55,616 [INFO] cox_model: ģ�� C-index: 0.5189
2025-05-27 14:28:55,617 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:28:55,617 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:28:55,617 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:28:55,618 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:28:55,618 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:28:55,618 [INFO] stable_cox_equipment: ������֤ C-index: 0.0000
2025-05-27 14:28:55,620 [INFO] utils_equipment: ģ���ѱ��浽: models\stable_cox_equipment_model.pkl
2025-05-27 14:28:55,620 [INFO] stable_cox_equipment: Stable Cox ģ��ѵ�����
2025-05-27 14:28:55,624 [INFO] stable_cox_equipment: ��ʼģ������
2025-05-27 14:28:55,624 [INFO] evaluation: ��ʼģ������
2025-05-27 14:28:55,627 [WARNING] evaluation: C-index ����ʧ��: 'weights'
2025-05-27 14:28:55,629 [WARNING] evaluation: ������Ȼ����ʧ��: 'weights'
2025-05-27 14:28:55,629 [WARNING] evaluation: AIC ����ʧ��: Since the model is semi-parametric (and not fully-parametric), the AIC does not exist. You probably want the `.AIC_partial_` property instead.
2025-05-27 14:28:55,629 [INFO] evaluation: ִ�� 3 ����շ���
2025-05-27 14:28:55,661 [INFO] evaluation: ���ɿ��ӻ�ͼ��
2025-05-27 14:29:10,934 [INFO] evaluation: ģ��������� - C-index: nan, AIC: nan
2025-05-27 14:29:10,936 [INFO] evaluation: ���������ѱ��浽: plots\evaluation_report.md
2025-05-27 14:30:50,742 [INFO] stable_cox_equipment: Stable Cox Equipment ģ�ͳ�ʼ�����
2025-05-27 14:30:50,744 [INFO] stable_cox_equipment: ��ʼ��� Stable Cox ģ��
2025-05-27 14:30:50,750 [WARNING] stable_cox_equipment: ���ݸ�ʽ����: ["��ȱʧ���� (>80%): ['PO1 PO Number_ERT', 'Item Description_ERT', 'Vendor Site Code_ERT', 'Vendor Country Code_ERT', 'Vendor Pole_ERT', 'Failure Code_ERT']"]
2025-05-27 14:30:50,751 [INFO] stable_cox_equipment: ʹ�� 162 ���������н�ģ
2025-05-27 14:30:50,753 [INFO] cox_model: ��ʼ����ѡ�񣬷���: cox_pvalue
2025-05-27 14:30:50,753 [INFO] cox_model: ��ʼ���� 162 ����ѡ����
2025-05-27 14:31:08,268 [INFO] cox_model: �ɹ������� 162 ������
2025-05-27 14:31:08,269 [INFO] cox_model: ����ѡ���� 6 ������
2025-05-27 14:31:08,269 [INFO] cox_model: ѡ�������: ['PULSE_280V_Z1_LOW', '300V_V700_L_Z', '300V_V280_H_X', 'PULSE_280V_Z2_LOW', '300V_V280_L_X']...
2025-05-27 14:31:08,269 [INFO] cox_model: ����ѡ����ɣ�ѡ���� 6 ������
2025-05-27 14:31:08,270 [INFO] stable_cox_equipment: ����ѡ����ɣ��� 162 ��������ѡ���� 6 ��
2025-05-27 14:31:08,270 [INFO] srdo_algorithm: ʹ�� SRDO �ؼ�Ȩ���ȶ�������: 3
2025-05-27 14:31:08,271 [INFO] srdo_algorithm: ��ʼ SRDO �㷨��������: 2927, ������: 6
2025-05-27 14:31:08,272 [INFO] srdo_algorithm: ԭʼ�����������: 14.1692
2025-05-27 14:31:08,560 [INFO] srdo_algorithm: SRDO ��ɣ�Ȩ��ͳ�� - ��ֵ: 1.0000, ��׼��: 0.0890, ���ֵ: 1.1621
2025-05-27 14:31:08,560 [INFO] stable_cox_equipment: SRDO �ؼ�Ȩ��ɣ�Ȩ��ͳ�� - ��ֵ: 1.0000, ��׼��: 0.0890
2025-05-27 14:31:08,561 [INFO] cox_model: ��ʼ��ϼ�Ȩ Cox ģ��
2025-05-27 14:31:08,561 [INFO] cox_model: ʹ�� 6 ���������н�ģ
2025-05-27 14:31:08,675 [INFO] cox_model: Cox ģ�������ɣ�������: 6
2025-05-27 14:31:08,688 [INFO] cox_model: ģ�� C-index: 0.5189
2025-05-27 14:31:08,689 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:31:08,690 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:31:08,690 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:31:08,691 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:31:08,691 [WARNING] stable_cox_equipment: ������֤�۵�ʧ��: 'StableCoxEquipment' object has no attribute 'feature_names_'
2025-05-27 14:31:08,691 [INFO] stable_cox_equipment: ������֤ C-index: 0.0000
2025-05-27 14:31:08,693 [INFO] utils_equipment: ģ���ѱ��浽: models\stable_cox_equipment_model.pkl
2025-05-27 14:31:08,693 [INFO] stable_cox_equipment: Stable Cox ģ��ѵ�����
2025-05-27 14:31:08,697 [INFO] stable_cox_equipment: ��ʼģ������
2025-05-27 14:31:08,697 [INFO] evaluation: ��ʼģ������
2025-05-27 14:31:08,701 [WARNING] evaluation: C-index ����ʧ��: 'weights'
2025-05-27 14:31:08,703 [WARNING] evaluation: ������Ȼ����ʧ��: 'weights'
2025-05-27 14:31:08,703 [WARNING] evaluation: AIC ����ʧ��: Since the model is semi-parametric (and not fully-parametric), the AIC does not exist. You probably want the `.AIC_partial_` property instead.
2025-05-27 14:31:08,703 [INFO] evaluation: ִ�� 3 ����շ���
2025-05-27 14:31:08,734 [INFO] evaluation: ���ɿ��ӻ�ͼ��
2025-05-27 14:31:22,836 [INFO] evaluation: ģ��������� - C-index: nan, AIC: nan
2025-05-27 14:31:22,837 [INFO] evaluation: ���������ѱ��浽: plots\evaluation_report.md
